<!--
  PolicyList.svelte
  
  A responsive insurance policy list component that displays customer policies in a card layout.
  
  Features:
  - Responsive CSS Grid layout (1-4 cards per row based on screen size)
  - Tailwind CSS styling with hover effects and transitions
  - Accessible markup with ARIA labels and semantic HTML
  - Color-coded status badges
  - Professional card design with policy details
  
  Responsive Breakpoints:
  - Mobile (< 640px): 1 card per row
  - Tablet (640px - 1024px): 2 cards per row  
  - Desktop (1024px - 1440px): 3 cards per row
  - Large screens (> 1440px): 4 cards per row
  
  Usage:
  <PolicyList />
  
  Future Enhancement: Accept policies as props
  <PolicyList {policies} />
-->

<script>
  import { onMount, createEventDispatcher } from "svelte";
  import { policiesStore, loadPolicies } from "./stores/dataStore.js";
  import {
    selectedMemberStore,
    selectedMemberDisplayName,
    selectedCitizenStore,
    selectedInsurerStore,
  } from "./stores/memberStore.js";
  import LoadingStates from "./components/LoadingStates.svelte";
  import ErrorStates from "./components/ErrorStates.svelte";
  import TwoStepMemberSelector from "./components/TwoStepMemberSelector.svelte";

  const dispatch = createEventDispatcher();

  // Reactive store subscriptions
  $: policies = $policiesStore.data || [];
  $: loading = $policiesStore.loading;
  $: error = $policiesStore.error;
  $: selectedMember = $selectedMemberStore;
  $: memberDisplayName = $selectedMemberDisplayName;
  $: selectedCitizen = $selectedCitizenStore;
  $: selectedInsurer = $selectedInsurerStore;

  // Check if both citizen and insurer are selected
  $: bothSelected = selectedCitizen && selectedInsurer;

  // Policy type icons mapping - updated for insurance plans
  const typeIcons = {
    Auto: "🚗",
    Home: "🏠",
    Life: "❤️",
    Health: "🏥",
    Medical: "🏥",
    Business: "🏢",
    General: "📄",
    Basic: "📋",
    Premium: "⭐",
    Executive: "💼",
  };

  // Status color classes for badges
  const statusColors = {
    Active: "bg-green-100 text-green-800 border-green-200",
    Inactive: "bg-gray-100 text-gray-800 border-gray-200",
    Expired: "bg-red-100 text-red-800 border-red-200",
    Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Cancelled: "bg-gray-100 text-gray-800 border-gray-200",
  };

  // Format date in Thai format
  function formatDate(dateString) {
    if (!dateString) return "ไม่ระบุ";
    const date = new Date(dateString);
    return date.toLocaleDateString("th-TH", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  }

  // Get policy type display name
  function getPolicyTypeDisplay(planName) {
    if (!planName) return "General";

    // Extract type from Thai plan names
    if (planName.includes("สุขภาพ")) return "Health";
    if (planName.includes("ชีวิต")) return "Life";
    if (planName.includes("Executive")) return "Executive";
    if (planName.includes("Premium")) return "Premium";
    if (planName.includes("Basic") || planName.includes("พื้นฐาน"))
      return "Basic";

    return "General";
  }

  // Get policy type icon
  function getPolicyIcon(planName) {
    const type = getPolicyTypeDisplay(planName);
    return typeIcons[type] || typeIcons.General;
  }

  // Handle policy card click to navigate to detail page
  function handlePolicyClick(memberCode) {
    dispatch("navigate", {
      page: "policy-detail",
      memberCode: memberCode,
    });
  }

  // Load policies data based on selected member
  async function loadPoliciesData() {
    if (!selectedMember) {
      console.warn("No member selected, cannot load policies");
      policiesStore.setError(new Error("กรุณาเลือกสมาชิกก่อนดูข้อมูลกรมธรรม์"));
      return;
    }

    if (!selectedMember.insurerCode || !selectedMember.citizenID) {
      console.warn("Selected member missing required data for API call");
      policiesStore.setError(new Error("ข้อมูลสมาชิกไม่ครบถ้วน"));
      return;
    }

    try {
      // Use member's insurerCode and citizenID for API call
      const searchParams = {
        insurerCode: selectedMember.insurerCode,
        citizenId: selectedMember.citizenID,
      };

      await loadPolicies(searchParams);
      console.log(
        `Policies loaded successfully for member ${selectedMember.memberCode}:`,
        $policiesStore.data?.length || 0,
      );
    } catch (error) {
      console.error("Failed to load policies:", error);
    }
  }

  // Retry function for error recovery
  async function handleRetry() {
    await loadPoliciesData();
  }

  // Reactive statement to reload policies when member changes
  $: if (selectedMember) {
    loadPoliciesData();
  }

  onMount(() => {
    console.log("PolicyList component mounted");
    // Initial load will be triggered by reactive statement when member is available
  });
</script>

<main
  class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8"
  aria-label="Insurance Policy List"
>
  <div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <header class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-6">กรมธรรม์ประกันภัย</h1>

      <!-- Two-Step Member Selector -->
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
      >
        <h2 class="text-lg font-semibold text-gray-900 mb-4">
          เลือกข้อมูลสมาชิก
        </h2>
        <TwoStepMemberSelector
          compact={false}
          showLabels={true}
          horizontal={true}
        />
      </div>

      <!-- Selection Status -->
      {#if selectedMember}
        <p class="text-gray-600">
          กรมธรรม์ของ <span class="font-semibold text-gray-800"
            >{memberDisplayName}</span
          >
          <span class="text-sm text-gray-500 ml-2"
            >({selectedMember.memberCode})</span
          >
        </p>
      {:else if selectedCitizen && selectedInsurer}
        <p class="text-gray-600">กำลังโหลดข้อมูลสมาชิก...</p>
      {:else}
        <p class="text-gray-600">
          กรุณาเลือกสมาชิกและบริษัทประกันเพื่อดูข้อมูลกรมธรรม์
        </p>
      {/if}
    </header>

    <!-- No Selection State -->
    {#if !bothSelected}
      <div class="text-center py-12">
        <div class="text-6xl mb-4" aria-hidden="true">👤</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">
          กรุณาเลือกข้อมูลสมาชิก
        </h2>
        <p class="text-gray-600 mb-6">
          กรุณาเลือกสมาชิกและบริษัทประกันจากด้านบนเพื่อดูข้อมูลกรมธรรม์
        </p>
      </div>

      <!-- Loading State -->
    {:else if loading}
      <LoadingStates
        variant="cards"
        count={6}
        message="กำลังโหลดข้อมูลกรมธรรม์..."
      />

      <!-- Error State -->
    {:else if error}
      <ErrorStates
        variant="api"
        {error}
        on:retry={handleRetry}
        message="ไม่สามารถโหลดข้อมูลกรมธรรม์ได้ กรุณาลองใหม่อีกครั้ง"
      />

      <!-- Empty State -->
    {:else if policies.length === 0}
      <div class="text-center py-12">
        <div class="text-6xl mb-4" aria-hidden="true">📄</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">
          ไม่พบข้อมูลกรมธรรม์
        </h2>
        <p class="text-gray-600 mb-6">
          ไม่พบกรมธรรม์ประกันภัยสำหรับสมาชิกท่านนี้
        </p>
        <button
          class="inline-flex items-center px-6 py-3
               bg-blue-600 hover:bg-blue-700
               text-white font-semibold rounded-lg
               transition-colors duration-200
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          on:click={handleRetry}
          aria-label="Refresh policies"
        >
          <svg
            class="mr-2 w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          รีเฟรช
        </button>
      </div>

      <!-- Policies Grid -->
    {:else}
      <section
        class="grid gap-6
             grid-cols-1
             sm:grid-cols-2
             lg:grid-cols-3
             xl:grid-cols-3"
        aria-label="Policy cards grid"
      >
        {#each policies as policy (policy.MemberCode || policy.PolicyNo)}
          <button
            class="bg-white rounded-lg shadow-md hover:shadow-lg
               transition-all duration-300 ease-in-out
               hover:scale-102 transform
               border border-gray-100
               min-h-[320px] p-6
               flex flex-col justify-between
               cursor-pointer text-left w-full"
            aria-labelledby="policy-{policy.MemberCode}-title"
            on:click={() => handlePolicyClick(policy.MemberCode)}
          >
            <!-- Policy Header -->
            <div class="mb-4">
              <div class="flex items-center justify-between mb-3">
                <h2
                  id="policy-{policy.MemberCode}-title"
                  class="text-lg font-semibold text-gray-900 flex items-center gap-2"
                >
                  <span class="text-2xl" aria-hidden="true"
                    >{getPolicyIcon(policy.PlanName)}</span
                  >
                  {getPolicyTypeDisplay(policy.PlanName)}
                </h2>
                <div class="flex flex-col items-end gap-1">
                  <span
                    class="px-3 py-1 rounded-full text-xs font-medium border
                       {statusColors[policy.MemberStatus] ||
                      statusColors.Active}"
                    aria-label="Member status: {policy.MemberStatus}"
                  >
                    {policy.MemberStatus}
                  </span>
                  {#if policy.VIP === "Y"}
                    <span
                      class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200"
                    >
                      VIP
                    </span>
                  {/if}
                </div>
              </div>

              <p class="text-sm text-gray-600 mb-2">
                กรมธรรม์ #{policy.PolicyNo}
              </p>
              <p class="text-sm text-gray-600 mb-2">
                ใบรับรอง #{policy.CertificateNo}
              </p>
            </div>

            <!-- Policy Details -->
            <div class="space-y-3 mb-4 flex-grow">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500">ประเภทสมาชิก:</span>
                <span class="text-sm font-medium text-gray-900">
                  {policy.MemberType}
                </span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500">ประเภทบัตร:</span>
                <span class="text-sm font-medium text-gray-900">
                  {policy.CardType}
                </span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500">วันที่เริ่มต้น:</span>
                <span class="text-sm font-medium text-gray-900">
                  {formatDate(policy.PlanEffFrom)}
                </span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500">วันที่สิ้นสุด:</span>
                <span class="text-sm font-medium text-gray-900">
                  {formatDate(policy.PlanEffTo)}
                </span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500">บริษัทประกัน:</span>
                <span class="text-sm font-medium text-gray-900 text-right">
                  {policy.InsurerName || policy.InsurerNameEN}
                </span>
              </div>
            </div>

            <!-- Plan Description -->
            <div class="border-t border-gray-100 pt-4">
              <p class="text-xs text-gray-600 leading-relaxed line-clamp-2">
                {policy.PlanName}
              </p>
              {#if policy.CompanyName}
                <p class="text-xs text-gray-500 mt-1">
                  {policy.CompanyName}
                </p>
              {/if}
            </div>
          </button>
        {/each}
      </section>
    {/if}
  </div>
</main>
