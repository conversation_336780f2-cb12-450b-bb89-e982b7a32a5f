var fs=Object.defineProperty;var ms=(t,e,l)=>e in t?fs(t,e,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[e]=l;var ql=(t,e,l)=>ms(t,typeof e!="symbol"?e+"":e,l);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))n(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&n(s)}).observe(document,{childList:!0,subtree:!0});function l(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(r){if(r.ep)return;r.ep=!0;const o=l(r);fetch(r.href,o)}})();function F(){}function $r(t){return t()}function Ri(){return Object.create(null)}function Je(t){t.forEach($r)}function tn(t){return typeof t=="function"}function pt(t,e){return t!=t?e==e:t!==e||t&&typeof t=="object"||typeof t=="function"}function ps(t){return Object.keys(t).length===0}function ln(t,...e){if(t==null){for(const n of e)n(void 0);return F}const l=t.subscribe(...e);return l.unsubscribe?()=>l.unsubscribe():l}function Ft(t){let e;return ln(t,l=>e=l)(),e}function Te(t,e,l){t.$$.on_destroy.push(ln(e,l))}function i(t,e){t.appendChild(e)}function x(t,e,l){t.insertBefore(e,l||null)}function N(t){t.parentNode&&t.parentNode.removeChild(t)}function Ie(t,e){for(let l=0;l<t.length;l+=1)t[l]&&t[l].d(e)}function f(t){return document.createElement(t)}function ne(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function v(t){return document.createTextNode(t)}function p(){return v(" ")}function kl(){return v("")}function ie(t,e,l,n){return t.addEventListener(e,l,n),()=>t.removeEventListener(e,l,n)}function a(t,e,l){l==null?t.removeAttribute(e):t.getAttribute(e)!==l&&t.setAttribute(e,l)}function gs(t){return Array.from(t.childNodes)}function T(t,e){e=""+e,t.data!==e&&(t.data=e)}function bs(t,e,{bubbles:l=!1,cancelable:n=!1}={}){return new CustomEvent(t,{detail:e,bubbles:l,cancelable:n})}let Ut;function jt(t){Ut=t}function qr(){if(!Ut)throw new Error("Function called outside component initialization");return Ut}function Cl(t){qr().$$.on_mount.push(t)}function nn(){const t=qr();return(e,l,{cancelable:n=!1}={})=>{const r=t.$$.callbacks[e];if(r){const o=bs(e,l,{cancelable:n});return r.slice().forEach(s=>{s.call(t,o)}),!o.defaultPrevented}return!0}}const It=[],Tl=[];let Nt=[];const Oi=[],hs=Promise.resolve();let Jl=!1;function _s(){Jl||(Jl=!0,hs.then(Yr))}function Kl(t){Nt.push(t)}const Yl=new Set;let Ct=0;function Yr(){if(Ct!==0)return;const t=Ut;do{try{for(;Ct<It.length;){const e=It[Ct];Ct++,jt(e),ys(e.$$)}}catch(e){throw It.length=0,Ct=0,e}for(jt(null),It.length=0,Ct=0;Tl.length;)Tl.pop()();for(let e=0;e<Nt.length;e+=1){const l=Nt[e];Yl.has(l)||(Yl.add(l),l())}Nt.length=0}while(It.length);for(;Oi.length;)Oi.pop()();Jl=!1,Yl.clear(),jt(t)}function ys(t){if(t.fragment!==null){t.update(),Je(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(Kl)}}function vs(t){const e=[],l=[];Nt.forEach(n=>t.indexOf(n)===-1?e.push(n):l.push(n)),l.forEach(n=>n()),Nt=e}const vl=new Set;let mt;function rn(){mt={r:0,c:[],p:mt}}function sn(){mt.r||Je(mt.c),mt=mt.p}function ue(t,e){t&&t.i&&(vl.delete(t),t.i(e))}function be(t,e,l,n){if(t&&t.o){if(vl.has(t))return;vl.add(t),mt.c.push(()=>{vl.delete(t),n&&(l&&t.d(1),n())}),t.o(e)}else n&&n()}function X(t){return(t==null?void 0:t.length)!==void 0?t:Array.from(t)}function ws(t,e){t.d(1),e.delete(t.key)}function Es(t,e,l,n,r,o,s,c,u,d,m,g){let b=t.length,_=o.length,y=b;const S={};for(;y--;)S[t[y].key]=y;const h=[],w=new Map,A=new Map,R=[];for(y=_;y--;){const P=g(r,o,y),C=l(P);let k=s.get(C);k?R.push(()=>k.p(P,e)):(k=d(C,P),k.c()),w.set(C,h[y]=k),C in S&&A.set(C,Math.abs(y-S[C]))}const z=new Set,I=new Set;function E(P){ue(P,1),P.m(c,m),s.set(P.key,P),m=P.first,_--}for(;b&&_;){const P=h[_-1],C=t[b-1],k=P.key,O=C.key;P===C?(m=P.first,b--,_--):w.has(O)?!s.has(k)||z.has(k)?E(P):I.has(O)?b--:A.get(k)>A.get(O)?(I.add(k),E(P)):(z.add(O),b--):(u(C,s),b--)}for(;b--;){const P=t[b];w.has(P.key)||u(P,s)}for(;_;)E(h[_-1]);return Je(R),h}function Ke(t){t&&t.c()}function Le(t,e,l){const{fragment:n,after_update:r}=t.$$;n&&n.m(e,l),Kl(()=>{const o=t.$$.on_mount.map($r).filter(tn);t.$$.on_destroy?t.$$.on_destroy.push(...o):Je(o),t.$$.on_mount=[]}),r.forEach(Kl)}function ze(t,e){const l=t.$$;l.fragment!==null&&(vs(l.after_update),Je(l.on_destroy),l.fragment&&l.fragment.d(e),l.on_destroy=l.fragment=null,l.ctx=[])}function Cs(t,e){t.$$.dirty[0]===-1&&(It.push(t),_s(),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function xt(t,e,l,n,r,o,s=null,c=[-1]){const u=Ut;jt(t);const d=t.$$={fragment:null,ctx:[],props:o,update:F,not_equal:r,bound:Ri(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(u?u.$$.context:[])),callbacks:Ri(),dirty:c,skip_bound:!1,root:e.target||u.$$.root};s&&s(d.root);let m=!1;if(d.ctx=l?l(t,e.props||{},(g,b,..._)=>{const y=_.length?_[0]:b;return d.ctx&&r(d.ctx[g],d.ctx[g]=y)&&(!d.skip_bound&&d.bound[g]&&d.bound[g](y),m&&Cs(t,g)),b}):[],d.update(),m=!0,Je(d.before_update),d.fragment=n?n(d.ctx):!1,e.target){if(e.hydrate){const g=gs(e.target);d.fragment&&d.fragment.l(g),g.forEach(N)}else d.fragment&&d.fragment.c();e.intro&&ue(t.$$.fragment),Le(t,e.target,e.anchor),Yr()}jt(u)}class Mt{constructor(){ql(this,"$$");ql(this,"$$set")}$destroy(){ze(this,1),this.$destroy=F}$on(e,l){if(!tn(l))return F;const n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(l),()=>{const r=n.indexOf(l);r!==-1&&n.splice(r,1)}}$set(e){this.$$set&&!ps(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const Ts="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(Ts);const Tt=[];function Is(t,e){return{subscribe:St(t,e).subscribe}}function St(t,e=F){let l;const n=new Set;function r(c){if(pt(t,c)&&(t=c,l)){const u=!Tt.length;for(const d of n)d[1](),Tt.push(d,t);if(u){for(let d=0;d<Tt.length;d+=2)Tt[d][0](Tt[d+1]);Tt.length=0}}}function o(c){r(c(t))}function s(c,u=F){const d=[c,u];return n.add(d),n.size===1&&(l=e(r,o)||F),c(t),()=>{n.delete(d),n.size===0&&l&&(l(),l=null)}}return{set:r,update:o,subscribe:s}}function Be(t,e,l){const n=!Array.isArray(t),r=n?[t]:t;if(!r.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=e.length<2;return Is(l,(s,c)=>{let u=!1;const d=[];let m=0,g=F;const b=()=>{if(m)return;g();const y=e(n?d[0]:d,s,c);o?s(y):g=tn(y)?y:F},_=r.map((y,S)=>ln(y,h=>{d[S]=h,m&=~(1<<S),u&&b()},()=>{m|=1<<S}));return u=!0,b(),function(){Je(_),g(),u=!1}})}const ks={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_ENVIRONMENT:"development",VITE_DEV_MODE:"true",VITE_TPA_API_BASE_URL:"http://localhost:9000",VITE_TPA_API_PREFIX:"/api",VITE_TPA_API_RETRY_ATTEMPTS:"3",VITE_TPA_API_TIMEOUT:"30000"};let Se={};try{Se=ks||{}}catch{Se={}}const Gl={devMode:Se.VITE_DEV_MODE==="true",apiEnvironment:Se.VITE_API_ENVIRONMENT||"development",mode:Se.MODE||"development",isDev:Se.DEV!==!1,isProd:Se.PROD===!0},Ge={baseUrl:Se.VITE_TPA_API_BASE_URL||"http://localhost:9000",prefix:Se.VITE_TPA_API_PREFIX||"/api",timeout:parseInt(Se.VITE_TPA_API_TIMEOUT||"30000"),retryAttempts:parseInt(Se.VITE_TPA_API_RETRY_ATTEMPTS||"3")},Gr=()=>`${Ge.baseUrl}${Ge.prefix}`,Ns=()=>{const t=[];return(isNaN(Ge.timeout)||Ge.timeout<=0)&&t.push("TPA_API_TIMEOUT must be a positive number"),(isNaN(Ge.retryAttempts)||Ge.retryAttempts<0)&&t.push("TPA_API_RETRY_ATTEMPTS must be a non-negative number"),{isValid:t.length===0,errors:t}};if(Gl.devMode&&Gl.isDev){console.log("Environment Configuration:",{client:Gl,tpaApi:Ge,fullApiUrl:Gr()});const t=Ns();t.isValid||console.warn("Environment validation errors:",t.errors)}class wl extends Error{constructor(e,l=null,n=null){super(e),this.name="ApiError",this.status=l,this.data=n,this.timestamp=new Date().toISOString()}getUserMessage(){return this.status===400?"Invalid request. Please check your input and try again.":this.status===404?"The requested information was not found.":this.status===422?"The provided data is invalid. Please check your input.":this.status>=500?"Server error. Please try again later.":this.message||"An unexpected error occurred."}isRetryable(){return this.status>=500}toJSON(){return{name:this.name,message:this.message,status:this.status,data:this.data,timestamp:this.timestamp}}}class El extends Error{constructor(e="Network error"){super(e),this.name="NetworkError",this.timestamp=new Date().toISOString()}getUserMessage(){return"Unable to connect to the server. Please check your internet connection and try again."}isRetryable(){return!0}toJSON(){return{name:this.name,message:this.message,timestamp:this.timestamp}}}class Zl extends Error{constructor(e="Request timeout"){super(e),this.name="TimeoutError",this.timestamp=new Date().toISOString()}getUserMessage(){return"The request took too long to complete. Please try again."}isRetryable(){return!0}toJSON(){return{name:this.name,message:this.message,timestamp:this.timestamp}}}class Jr extends Error{constructor(e,l=null,n=null){super(e),this.name="ValidationError",this.field=l,this.value=n,this.timestamp=new Date().toISOString()}getUserMessage(){return this.field?`Invalid value for ${this.field}. ${this.message}`:this.message||"Invalid input provided."}isRetryable(){return!1}toJSON(){return{name:this.name,message:this.message,field:this.field,value:this.value,timestamp:this.timestamp}}}function xs(t,e={}){const l={type:t.name||"Error",message:t.message||"Unknown error",userMessage:t.getUserMessage?t.getUserMessage():t.message,isRetryable:t.isRetryable?t.isRetryable():!1,timestamp:t.timestamp||new Date().toISOString(),context:e};return t.status&&(l.status=t.status),t.data&&(l.data=t.data),t.field&&(l.field=t.field),t.retryAfter&&(l.retryAfter=t.retryAfter),l}function Ze(t,e={}){const l=xs(t,e);t instanceof Jr?console.warn("Validation Error:",l):t instanceof El||t instanceof Zl?console.warn("Network Error:",l):t instanceof wl&&t.status>=500?console.error("Server Error:",l):console.error("API Error:",l)}class Ms{constructor(e={}){this.baseUrl=e.baseUrl||Gr(),this.timeout=e.timeout||Ge.timeout,this.retryAttempts=e.retryAttempts||Ge.retryAttempts,this.retryDelay=e.retryDelay||1e3,this.requestInterceptors=[],this.responseInterceptors=[]}addRequestInterceptor(e){this.requestInterceptors.push(e)}addResponseInterceptor(e){this.responseInterceptors.push(e)}async applyRequestInterceptors(e){let l={...e};for(const n of this.requestInterceptors)l=await n(l);return l}async applyResponseInterceptors(e){let l=e;for(const n of this.responseInterceptors)l=await n(l);return l}calculateRetryDelay(e){return this.retryDelay*Math.pow(2,e)+Math.random()*1e3}isRetryableError(e){return e instanceof El||e instanceof Zl?!0:e instanceof wl?e.status>=500:!1}createTimeoutController(){const e=new AbortController,l=setTimeout(()=>{e.abort()},this.timeout);return{controller:e,timeoutId:l}}async request(e,l={}){const n=`${this.baseUrl}${e}`;let r;const o=await this.applyRequestInterceptors({url:n,...l});for(let s=0;s<=this.retryAttempts;s++){const{controller:c,timeoutId:u}=this.createTimeoutController();try{const d={method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json",...o.headers},signal:c.signal,...o};delete d.url;const m=await fetch(o.url,d);clearTimeout(u);const g=await this.applyResponseInterceptors(m);if(!g.ok){const _=await this.parseErrorResponse(g);throw new wl(_.message||`HTTP ${g.status}`,g.status,_)}return await g.json()}catch(d){if(clearTimeout(u),r=this.handleRequestError(d),s===this.retryAttempts||!this.isRetryableError(r))throw r;const m=this.calculateRetryDelay(s);await new Promise(g=>setTimeout(g,m))}}throw r}async parseErrorResponse(e){try{return await e.json()}catch{return{error:"Unknown Error",message:`HTTP ${e.status} ${e.statusText}`,details:{}}}}handleRequestError(e){return e.name==="AbortError"||e instanceof DOMException&&e.name==="AbortError"?new Zl("Request timeout"):e instanceof TypeError&&e.message.includes("fetch")?new El("Network error - unable to connect to API"):e instanceof wl?e:new El(e.message||"Unknown network error")}async get(e,l={},n={}){const r=this.buildQueryString(l),o=r?`${e}?${r}`:e;return this.request(o,{method:"GET",...n})}buildQueryString(e){const l=new URLSearchParams;return Object.entries(e).forEach(([n,r])=>{r!=null&&r!==""&&l.append(n,String(r))}),l.toString()}async healthCheck(){return this.request("/health")}}const it=new Ms,Hi={INSURER_CITIZEN:["INSURER_CODE","CITIZEN_ID"],INSURER_POLICY_TH:["INSURER_CODE","POLICY_NO","NAME_TH"],INSURER_POLICY_EN:["INSURER_CODE","POLICY_NO","NAME_EN"],INSURER_CERT_TH:["INSURER_CODE","CERTIFICATE_NO","NAME_TH"],INSURER_CERT_EN:["INSURER_CODE","CERTIFICATE_NO","NAME_EN"],INSURER_STAFF_TH:["INSURER_CODE","STAFF_NO","NAME_TH"],INSURER_STAFF_EN:["INSURER_CODE","STAFF_NO","NAME_EN"],INSURER_OTHER:["INSURER_CODE","OTHER_ID"],INSURER_NAME_TH:["INSURER_CODE","NAME_TH"],INSURER_NAME_EN:["INSURER_CODE","NAME_EN"]},Li={MEMBER:["MEMBER_CODE"],INSURER_CITIZEN:["INSURER_CODE","CITIZEN_ID"]},ge={INSURER_CODE:["INS001","INS002","INS003"],CITIZEN_ID:["1234567890123","1234567890124","1234567890125","1234567890126","1234567890127","1234567890128","1234567890129","1234567890130","1234567890131","1234567890132","1234567890133","1234567890134"],POLICY_NO:["POL001","POL002","POL003","POL004","POL005","POL006","POL007","POL008","POL009","POL010"],CERTIFICATE_NO:["CERT001","CERT002","CERT003","CERT004","CERT005","CERT006","CERT007","CERT008","CERT009","CERT010","CERT011","CERT012"],STAFF_NO:["ST001","ST002","ST003","ST004","ST005","ST006","ST007","ST008","ST009","ST010","ST011","ST012"],OTHER_ID:["EMP001","EMP002","EMP003","EMP004","EMP005","EMP006","EMP007","EMP008","EMP009","EMP010","EMP011","EMP012"],MEMBER_CODE:["MEM001","MEM002","MEM003","MEM004","MEM005","MEM006","MEM007","MEM008","MEM009","MEM010","MEM011","MEM012"],NAME_TH:["สมชาย ใจดี","สุดา รักดี","มาลี สวยงาม","วิชัย เก่งมาก","นิดา ขยันดี","ประยุทธ มั่นคง","สมหญิง ดีใจ","อนุชา รวยมาก","ปิยะดา สุขใส","ธนากร เจริญรุ่ง","สมศักดิ์ หมดอายุ","วรรณา ใหม่สด"],NAME_EN:["Somchai Jaidee","Suda Rakdee","Malee Suayngam","Wichai Kengmak","Nida Khayanee","Prayuth Mankong","Somying Deejai","Anucha Ruaimak","Piyada Suksai","Thanakorn Charoenrung","Somsak Modayu","Wanna Maisod"]},Ss={PRINCIPAL:"Principal",DEPENDENT:"Dependent"},Ps={STANDARD:"Standard",GOLD:"Gold",PLATINUM:"Platinum",DIAMOND:"Diamond"},Ds={TH:"TH",EN:"EN"},As={YES:"Y",NO:"N"},Rs={ACTIVE:"Active",INACTIVE:"Inactive",SUSPENDED:"Suspended"},ft={isValidInsurerCode(t){return ge.INSURER_CODE.includes(t)},isValidCitizenId(t){return ge.CITIZEN_ID.includes(t)},isValidMemberCode(t){return ge.MEMBER_CODE.includes(t)},isValidPolicyNo(t){return ge.POLICY_NO.includes(t)},isValidNameTh(t){return ge.NAME_TH.includes(t)},isValidNameEn(t){return ge.NAME_EN.includes(t)},isValidMemberStatus(t){return Object.values(Rs).includes(t)},isValidMemberType(t){return Object.values(Ss).includes(t)},isValidVipStatus(t){return Object.values(As).includes(t)},isValidCardType(t){return Object.values(Ps).includes(t)},isValidLanguage(t){return Object.values(Ds).includes(t)},validateMember(t){const e=[];if(!t)return{isValid:!1,errors:["Member object is required"]};const l=["memberCode","memberStatus","memberType"];for(const n of l)t[n]||e.push(`${n} is required`);return t.memberCode&&!this.isValidMemberCode(t.memberCode)&&e.push("Invalid member code"),t.memberStatus&&!this.isValidMemberStatus(t.memberStatus)&&e.push("Invalid member status"),t.memberType&&!this.isValidMemberType(t.memberType)&&e.push("Invalid member type"),t.vip&&!this.isValidVipStatus(t.vip)&&e.push("Invalid VIP status"),t.cardType&&!this.isValidCardType(t.cardType)&&e.push("Invalid card type"),t.language&&!this.isValidLanguage(t.language)&&e.push("Invalid language"),{isValid:e.length===0,errors:e}}};function Os(t){const e=[],l=[],n=an(t),r=Object.keys(n);if(r.length===0)return e.push("At least one parameter combination is required"),{isValid:!1,errors:e,warnings:l,cleanParams:n};const o=Kr(r,Hi);if(!o)return e.push(`Invalid parameter combination. Must use exactly one of: ${Zr(Hi)}`),{isValid:!1,errors:e,warnings:l,cleanParams:n};const s=Wr(n);return e.push(...s.errors),l.push(...s.warnings),{isValid:e.length===0,errors:e,warnings:l,cleanParams:n,combination:o}}function Hs(t){const e=[],l=[],n=an(t),r=Object.keys(n);if(r.length===0)return e.push("At least one parameter combination is required"),{isValid:!1,errors:e,warnings:l,cleanParams:n};const o=Kr(r,Li);if(!o)return e.push(`Invalid parameter combination. Must use exactly one of: ${Zr(Li)}`),{isValid:!1,errors:e,warnings:l,cleanParams:n};const s=Wr(n);return e.push(...s.errors),l.push(...s.warnings),{isValid:e.length===0,errors:e,warnings:l,cleanParams:n,combination:o}}function Ls(t){const e=[],l=[],n=an(t);return n.MEMBER_CODE?(ft.isValidMemberCode(n.MEMBER_CODE)||e.push(`Invalid MEMBER_CODE: ${n.MEMBER_CODE}`),{isValid:e.length===0,errors:e,warnings:l,cleanParams:n}):(e.push("MEMBER_CODE is required"),{isValid:!1,errors:e,warnings:l,cleanParams:n})}function an(t){const e={};return Object.entries(t||{}).forEach(([l,n])=>{n!=null&&n!==""&&(e[l]=String(n).trim())}),e}function Kr(t,e){const l=[...t].sort();for(const[n,r]of Object.entries(e)){const o=[...r].sort();if(zs(l,o))return n}return null}function zs(t,e){return t.length===e.length&&t.every((l,n)=>l===e[n])}function Zr(t){return Object.entries(t).map(([e,l])=>`(${l.join(" + ")})`).join(", ")}function Wr(t){const e=[],l=[];return Object.entries(t).forEach(([n,r])=>{switch(n){case"INSURER_CODE":ft.isValidInsurerCode(r)||e.push(`Invalid INSURER_CODE: ${r}. Valid values: ${ge.INSURER_CODE.join(", ")}`);break;case"CITIZEN_ID":ft.isValidCitizenId(r)||e.push(`Invalid CITIZEN_ID: ${r}`);break;case"MEMBER_CODE":ft.isValidMemberCode(r)||e.push(`Invalid MEMBER_CODE: ${r}. Valid values: ${ge.MEMBER_CODE.join(", ")}`);break;case"POLICY_NO":ft.isValidPolicyNo(r)||e.push(`Invalid POLICY_NO: ${r}. Valid values: ${ge.POLICY_NO.join(", ")}`);break;case"NAME_TH":ft.isValidNameTh(r)||l.push(`NAME_TH "${r}" may not match exactly. Names are case-sensitive.`);break;case"NAME_EN":ft.isValidNameEn(r)||l.push(`NAME_EN "${r}" may not match exactly. Names are case-sensitive.`);break;case"CERTIFICATE_NO":ge.CERTIFICATE_NO.includes(r)||e.push(`Invalid CERTIFICATE_NO: ${r}. Valid values: ${ge.CERTIFICATE_NO.join(", ")}`);break;case"STAFF_NO":ge.STAFF_NO.includes(r)||e.push(`Invalid STAFF_NO: ${r}. Valid values: ${ge.STAFF_NO.join(", ")}`);break;case"OTHER_ID":ge.OTHER_ID.includes(r)||e.push(`Invalid OTHER_ID: ${r}. Valid values: ${ge.OTHER_ID.join(", ")}`);break;default:l.push(`Unknown parameter: ${n}`)}}),{errors:e,warnings:l}}function Bs(t,e,l){const n=new Jr(t);return n.data={validationErrors:e,originalParams:l},n}function on(t,e){const l=e(t);if(!l.isValid)throw Bs(l.errors[0]||"Validation failed",l.errors,t);return l.cleanParams}async function Nl(t,e={}){try{const l=on(t,Os),n=await it.get("/PolicyListSF",l,e),r=n.data||n;return{success:!0,data:r,total:n.total||(Array.isArray(r)?r.length:r?1:0),params:l}}catch(l){throw Ze(l,{endpoint:"PolicyListSF",params:t}),l}}async function Wl(t,e={}){try{const n=on({MEMBER_CODE:t},Ls);return{success:!0,data:await it.get("/PolicyDetailSF",n,e),memberCode:n.MEMBER_CODE}}catch(l){throw Ze(l,{endpoint:"PolicyDetailSF",memberCode:t}),l}}async function cn(t,e={}){try{const l=on(t,Hs),n=await it.get("/ClaimListSF",l,e),r=n.data||n;return{success:!0,data:r,total:n.total||(Array.isArray(r)?r.length:r?1:0),params:l}}catch(l){throw Ze(l,{endpoint:"ClaimListSF",params:t}),l}}async function Vs(t={}){try{return{success:!0,data:await it.healthCheck(t),timestamp:new Date().toISOString()}}catch(e){throw Ze(e,{endpoint:"health"}),e}}async function js(t={}){try{return{success:!0,data:await it.get("/",{},t),timestamp:new Date().toISOString()}}catch(e){throw Ze(e,{endpoint:"root"}),e}}async function Us(t,e="INS001",l={}){return Nl({INSURER_CODE:e,CITIZEN_ID:t},l)}async function Fs(t,e,l="INS001",n={}){const r=/[\u0E00-\u0E7F]/.test(e),o={INSURER_CODE:l,POLICY_NO:t};return r?o.NAME_TH=e:o.NAME_EN=e,Nl(o,n)}async function $s(t,e="INS001",l={}){const n=/[\u0E00-\u0E7F]/.test(t),r={INSURER_CODE:e};return n?r.NAME_TH=t:r.NAME_EN=t,Nl(r,l)}async function qs(t,e="INS001",l={}){return cn({INSURER_CODE:e,CITIZEN_ID:t},l)}async function Ql(t,e={}){return cn({MEMBER_CODE:t},e)}async function Ys(t,e={}){try{const[l,n]=await Promise.all([Wl(t,e),Ql(t,e)]);return{success:!0,memberCode:t,policy:l.data,claims:n.data,timestamp:new Date().toISOString()}}catch(l){throw Ze(l,{endpoint:"getMemberData",memberCode:t}),l}}async function Gs(t={}){try{return{success:!0,data:await it.get("/members",{},t),message:"Member list retrieved successfully",timestamp:new Date().toISOString()}}catch(e){throw Ze(e,{endpoint:"getAllMembersData"}),e}}async function Js(t,e={}){try{const l={MEMBER_CODE:t};return{success:!0,data:await it.get("/member/policies",l,e),memberCode:t,message:"Member policies retrieved successfully",timestamp:new Date().toISOString()}}catch(l){throw Ze(l,{endpoint:"getMemberPolicies",memberCode:t}),l}}async function Ks(t,e={}){try{const l={MEMBER_CODE:t};return{success:!0,data:await it.get("/member/claims",l,e),memberCode:t,message:"Member claims retrieved successfully",timestamp:new Date().toISOString()}}catch(l){throw Ze(l,{endpoint:"getMemberClaims",memberCode:t}),l}}const Ye={policies:{search:Nl,getDetail:Wl,searchByCitizenId:Us,searchByName:$s,searchByPolicyAndName:Fs},claims:{getList:cn,getByCitizenId:qs,getByMemberCode:Ql},members:{getAll:Gs,getData:Ys,getPolicyDetail:Wl,getClaims:Ql,getPolicies:Js,getClaimsData:Ks},system:{health:Vs,info:js}},Qr=[{memberCode:"MEM001",citizenID:"CIT001",insurerCode:"INS001"},{memberCode:"MEM002",citizenID:"CIT001",insurerCode:"INS002"},{memberCode:"MEM003",citizenID:"CIT001",insurerCode:"INS003"},{memberCode:"MEM004",citizenID:"CIT002",insurerCode:"INS001"},{memberCode:"MEM005",citizenID:"CIT002",insurerCode:"INS002"},{memberCode:"MEM006",citizenID:"CIT003",insurerCode:"INS001"},{memberCode:"MEM007",citizenID:"CIT003",insurerCode:"INS002"},{memberCode:"MEM008",citizenID:"CIT003",insurerCode:"INS003"}];function Xr(t){const l={CIT001:{titleTH:"นาย",nameTH:"สมชาย",surnameTH:"ใจดี",titleEN:"Mr.",nameEN:"Somchai",surnameEN:"Jaidee"},CIT002:{titleTH:"นาง",nameTH:"มาลี",surnameTH:"สวยงาม",titleEN:"Mrs.",nameEN:"Malee",surnameEN:"Suayngam"},CIT003:{titleTH:"นางสาว",nameTH:"ปิยะดา",surnameTH:"สุขใส",titleEN:"Ms.",nameEN:"Piyada",surnameEN:"Suksai"}}[t.citizenID]||{titleTH:"นาย",nameTH:"ไม่ระบุ",surnameTH:"ชื่อ",titleEN:"Mr.",nameEN:"Unknown",surnameEN:"Name"};return{...t,...l,memberStatus:"Active",memberType:"Principal",principleMemberCode:t.memberCode,principleName:`${l.nameTH} ${l.surnameTH}`,vip:"N",vipRemarks:"",cardType:"Standard",language:"TH",birthDate:null,gender:null,citizenship:"Thai",countryCode:"TH",mobile:null,email:null}}function gt(t=!1){return Qr.map(e=>Xr(e))}function es(t){const e=Qr.find(l=>l.memberCode===t);return e?Xr(e):null}function xl(t,e="TH"){return t?t.titleTH&&t.nameTH&&t.surnameTH?`${t.titleTH}${t.nameTH} ${t.surnameTH}`:t.titleEN&&t.nameEN&&t.surnameEN?`${t.titleEN} ${t.nameEN} ${t.surnameEN}`:t.memberCode||"Unknown Member":""}function ts(t,e="TH"){return t?t.nameTH?t.nameTH:t.nameEN?t.nameEN:t.memberCode||"Unknown":""}function Zs(t=!1){return gt(t).map(l=>({value:l.memberCode,label:xl(l,"TH"),shortLabel:ts(l,"TH"),status:l.memberStatus,vip:l.vip==="Y",cardType:l.cardType,memberType:l.memberType,language:l.language,member:l}))}function ls(){const t=gt(!1);return t.length>0?t[0]:null}function Ws(t){return t&&t.vip==="Y"}function Qs(t){return!t||!t.cardType?"":`${{Standard:"🟢",Gold:"🟡",Platinum:"⚪",Diamond:"💎"}[t.cardType]||""} ${t.cardType}`}const Xs={INS001:"บริษัท ประกันภัย เอ จำกัด (Insurance Company A Ltd.)",INS002:"Health Insurance Co. Ltd.",INS003:"บริษัท ประกันชีวิต ซี จำกัด (Life Insurance C Ltd.)"},ea={CIT001:{titleTH:"นาย",nameTH:"สมชาย",surnameTH:"ใจดี",titleEN:"Mr.",nameEN:"Somchai",surnameEN:"Jaidee"},CIT002:{titleTH:"นาง",nameTH:"มาลี",surnameTH:"สวยงาม",titleEN:"Mrs.",nameEN:"Malee",surnameEN:"Suayngam"},CIT003:{titleTH:"นางสาว",nameTH:"ปิยะดา",surnameTH:"สุขใส",titleEN:"Ms.",nameEN:"Piyada",surnameEN:"Suksai"}};function ns(t){return Xs[t]||t||"Unknown Insurer"}function zi(t,e="TH"){const l=ea[t];return l?e==="EN"?`${l.titleEN} ${l.nameEN} ${l.surnameEN}`:`${l.titleTH} ${l.nameTH} ${l.surnameTH}`:t||"Unknown Citizen"}function ta(){const t=gt(),e=new Map;return t.forEach(l=>{e.has(l.citizenID)||e.set(l.citizenID,{citizenID:l.citizenID,displayName:zi(l.citizenID,"TH"),displayNameEN:zi(l.citizenID,"EN"),titleTH:l.titleTH,nameTH:l.nameTH,surnameTH:l.surnameTH,titleEN:l.titleEN,nameEN:l.nameEN,surnameEN:l.surnameEN})}),Array.from(e.values()).sort((l,n)=>l.displayName.localeCompare(n.displayName,"th"))}function la(t){if(!t)return[];const e=gt(),l=new Map;return e.filter(n=>n.citizenID===t).forEach(n=>{l.has(n.insurerCode)||l.set(n.insurerCode,{insurerCode:n.insurerCode,displayName:ns(n.insurerCode),memberCode:n.memberCode})}),Array.from(l.values()).sort((n,r)=>n.displayName.localeCompare(r.displayName,"th"))}function na(){const t=gt(),e=new Map;return t.forEach(l=>{e.has(l.insurerCode)||e.set(l.insurerCode,{insurerCode:l.insurerCode,displayName:ns(l.insurerCode)})}),Array.from(e.values()).sort((l,n)=>l.displayName.localeCompare(n.displayName,"th"))}function ia(t,e){return!t||!e?null:gt().find(n=>n.citizenID===t&&n.insurerCode===e)||null}function ra(){return ta().map(e=>({value:e.citizenID,label:e.displayName,labelEN:e.displayNameEN,citizen:e}))}function sa(t=null){return(t?la(t):na()).map(l=>({value:l.insurerCode,label:l.displayName,insurer:l}))}const Il="insurance_portal_selected_member",is="insurance_portal_selected_citizen",un="insurance_portal_selected_insurer";function aa(){if(typeof window>"u")return null;try{const t=sessionStorage.getItem(Il);return t?JSON.parse(t):null}catch(t){return console.warn("Failed to parse stored member data:",t),null}}function rs(t){if(!(typeof window>"u"))try{t?sessionStorage.setItem(Il,JSON.stringify({memberCode:t,timestamp:Date.now()})):sessionStorage.removeItem(Il)}catch(e){console.warn("Failed to save member data to session storage:",e)}}function oa(){const t=aa();if(t&&t.memberCode){const e=es(t.memberCode);if(e)return e}return ls()}function ca(){if(typeof window>"u")return null;try{const t=sessionStorage.getItem(is);if(t){const e=JSON.parse(t);return console.log(`Restored citizen selection from session: ${e.displayName} (${e.citizenID})`),e}}catch(t){console.warn("Failed to restore citizen selection from session storage:",t)}return null}function ua(){if(typeof window>"u")return null;try{const t=sessionStorage.getItem(un);if(t){const e=JSON.parse(t);return console.log(`Restored insurer selection from session: ${e.displayName} (${e.insurerCode})`),e}}catch(t){console.warn("Failed to restore insurer selection from session storage:",t)}return null}const Vt=St({data:[],loading:!1,error:null,lastUpdated:null}),me=St(oa()),Pt=St(ca()),Dt=St(ua());Be(me,t=>(t==null?void 0:t.memberCode)||null);const dn=Be(me,t=>t?xl(t,"TH"):""),da=Be(me,t=>t?ts(t,"TH"):"");Be(me,t=>Ws(t));Be(me,t=>t?Qs(t):"");Be(Vt,t=>t.data&&t.data.length>0?Zs(!1):[]);Be(Pt,t=>(t==null?void 0:t.citizenID)||null);Be(Dt,t=>(t==null?void 0:t.insurerCode)||null);Be([Pt,Dt],([t,e])=>t&&e?{citizenID:t.citizenID,insurerCode:e.insurerCode,displayName:`${t.displayName} - ${e.displayName}`}:null);async function fa(t=!1){const e=Ft(Vt);if(!t&&e.data&&e.data.length>0)return e.data;Vt.update(l=>({...l,loading:!0,error:null}));try{await new Promise(n=>setTimeout(n,100));const l=gt(!1);return Vt.update(n=>({...n,data:l,loading:!1,error:null,lastUpdated:Date.now()})),l}catch(l){throw console.error("Failed to load member list:",l),Vt.update(n=>({...n,loading:!1,error:l.message||"Failed to load member list"})),l}}function ma(t){if(!t){console.warn("Member code is required for selection");return}const e=es(t);if(!e){console.warn(`Member with code ${t} not found`);return}me.set(e),rs(t),console.log(`Selected member: ${xl(e,"TH")} (${t})`)}function pa(t,e){if(!t||!e){console.warn("Citizen ID and data are required for selection");return}Pt.set(e),Dt.set(null),me.set(null);try{typeof window<"u"&&(sessionStorage.setItem(is,JSON.stringify(e)),sessionStorage.removeItem(un),sessionStorage.removeItem(Il))}catch(l){console.warn("Failed to save citizen selection to session storage:",l)}console.log(`Selected citizen: ${e.displayName} (${t})`)}function ga(t,e){if(!t||!e){console.warn("Insurer code and data are required for selection");return}const l=Ft(Pt);if(!l){console.warn("No citizen selected. Please select a citizen first.");return}Dt.set(e);const n=ia(l.citizenID,t);n&&(me.set(n),rs(n.memberCode));try{typeof window<"u"&&sessionStorage.setItem(un,JSON.stringify(e))}catch(r){console.warn("Failed to save insurer selection to session storage:",r)}console.log(`Selected insurer: ${e.displayName} (${t})`)}async function ba(){try{if(await fa(),!Ft(me)){const e=ls();e&&ma(e.memberCode)}console.log("Member store initialized successfully")}catch(t){console.error("Failed to initialize member store:",t)}}typeof window<"u"&&window.location.hostname==="localhost"&&me.subscribe(t=>{console.log("Selected member changed:",t?`${t.memberCode} - ${xl(t,"TH")}`:"None")});const ha=5*60*1e3,Xl=new Map;function ss(t,e={},l=!0){let n=[t];if(l){const o=Ft(me);o&&o.memberCode&&n.push(`member:${o.memberCode}`)}const r=Object.keys(e).sort().map(o=>`${o}:${e[o]}`).join("|");return r&&n.push(r),n.join("_")}function _a(t){return t&&Date.now()-t.timestamp<ha}function as(t){const e=Xl.get(t);return _a(e)?e.data:(Xl.delete(t),null)}function os(t,e){Xl.set(t,{data:e,timestamp:Date.now()})}function cs(t={}){const{subscribe:e,set:l,update:n}=St({data:null,loading:!1,error:null,lastUpdated:null,...t});return{subscribe:e,set:l,update:n,setLoading:r=>n(o=>({...o,loading:r,error:r?null:o.error})),setData:r=>n(o=>({...o,data:r,loading:!1,error:null,lastUpdated:Date.now()})),setError:r=>n(o=>({...o,error:r,loading:!1})),reset:()=>l({data:null,loading:!1,error:null,lastUpdated:null})}}const lt=cs(),kt=cs();Be(lt,t=>{var e;return((e=t.data)==null?void 0:e.filter(l=>l.status==="Active"))||[]});async function ya(t={},e=!1){const l=Ft(me),n=ss("policies",t,!0);if(!e){const r=as(n);if(r)return lt.setData(r),r}lt.setLoading(!0);try{let r;if(t.insurerCode&&t.citizenId?r=await Ye.policies.searchByCitizenId(t.citizenId,t.insurerCode):t.citizenId?r=await Ye.policies.searchByCitizenId(t.citizenId):t.policyNumber&&t.name?r=await Ye.policies.searchByPolicyAndName(t.policyNumber,t.name):t.name?r=await Ye.policies.searchByName(t.name):l&&l.citizenID&&l.insurerCode?r=await Ye.policies.searchByCitizenId(l.citizenID,l.insurerCode):l&&l.citizenID?r=await Ye.policies.searchByCitizenId(l.citizenID):r=await Ye.policies.searchByCitizenId("1234567890123"),r.success&&r.data){const o=r.data;return os(n,o),lt.setData(o),o}else throw new Error(r.message||"Failed to load policies")}catch(r){throw console.error("Error loading policies:",r),lt.setError(r),r}}async function va(t,e=!1){if(!t){const n=new Error("Member code is required");throw kt.setError(n),n}const l=ss("policy_detail",{memberCode:t});if(!e){const n=as(l);if(n)return kt.setData(n),n}kt.setLoading(!0);try{const n=await Ye.members.getPolicyDetail(t);if(n.success&&n.data){const r=n.data;return os(l,r),kt.setData(r),r}else throw new Error(n.message||"Failed to load policy details")}catch(n){throw console.error("Error loading policy detail:",n),kt.setError(n),n}}function Bi(t,e,l){const n=t.slice();return n[4]=e[l],n[6]=l,n}function Vi(t,e,l){const n=t.slice();return n[4]=e[l],n[6]=l,n}function wa(t,e,l){const n=t.slice();return n[4]=e[l],n[9]=l,n}function ji(t,e,l){const n=t.slice();return n[4]=e[l],n[6]=l,n}function Ea(t,e,l){const n=t.slice();return n[4]=e[l],n[9]=l,n}function Ui(t,e,l){const n=t.slice();return n[4]=e[l],n[6]=l,n}function Ca(t){let e,l,n,r,o,s=t[3]&&Fi(t);return{c(){e=f("div"),l=ne("svg"),n=ne("circle"),r=ne("path"),o=p(),s&&s.c(),a(n,"class","opacity-25"),a(n,"cx","12"),a(n,"cy","12"),a(n,"r","10"),a(n,"stroke","currentColor"),a(n,"stroke-width","4"),a(r,"class","opacity-75"),a(r,"fill","currentColor"),a(r,"d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"),a(l,"class","animate-spin w-4 h-4"),a(l,"fill","none"),a(l,"viewBox","0 0 24 24"),a(e,"class","inline-flex items-center gap-2 text-gray-600"),a(e,"role","status"),a(e,"aria-label","Loading")},m(c,u){x(c,e,u),i(e,l),i(l,n),i(l,r),i(e,o),s&&s.m(e,null)},p(c,u){c[3]?s?s.p(c,u):(s=Fi(c),s.c(),s.m(e,null)):s&&(s.d(1),s=null)},d(c){c&&N(e),s&&s.d()}}}function Ta(t){let e,l,n=X(Array(t[1])),r=[];for(let s=0;s<n.length;s+=1)r[s]=$i(Bi(t,n,s));let o=t[3]&&qi(t);return{c(){e=f("div");for(let s=0;s<r.length;s+=1)r[s].c();l=p(),o&&o.c(),a(e,"class","space-y-4"),a(e,"role","status"),a(e,"aria-label","Loading list")},m(s,c){x(s,e,c);for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(e,null);i(e,l),o&&o.m(e,null)},p(s,c){if(c&2){n=X(Array(s[1]));let u;for(u=0;u<n.length;u+=1){const d=Bi(s,n,u);r[u]?r[u].p(d,c):(r[u]=$i(),r[u].c(),r[u].m(e,l))}for(;u<r.length;u+=1)r[u].d(1);r.length=n.length}s[3]?o?o.p(s,c):(o=qi(s),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},d(s){s&&N(e),Ie(r,s),o&&o.d()}}}function Ia(t){let e,l,n,r,o,s,c,u,d=X(Array(3)),m=[];for(let y=0;y<d.length;y+=1)m[y]=Yi(ji(t,d,y));let g=X(Array(2)),b=[];for(let y=0;y<g.length;y+=1)b[y]=Gi(Vi(t,g,y));let _=t[3]&&Ji(t);return{c(){e=f("div"),l=f("div"),l.innerHTML='<div class="flex flex-col lg:flex-row lg:items-center lg:justify-between"><div class="flex items-center mb-4 lg:mb-0"><div class="w-12 h-12 bg-gray-200 rounded mr-4"></div> <div><div class="w-48 h-8 bg-gray-200 rounded mb-2"></div> <div class="w-32 h-5 bg-gray-200 rounded"></div></div></div> <div class="flex flex-col sm:flex-row sm:items-center gap-3"><div class="w-20 h-8 bg-gray-200 rounded-full"></div> <div class="text-right"><div class="w-24 h-8 bg-gray-200 rounded mb-1"></div> <div class="w-16 h-4 bg-gray-200 rounded"></div></div></div></div>',n=p(),r=f("div"),o=f("div");for(let y=0;y<m.length;y+=1)m[y].c();s=p(),c=f("div");for(let y=0;y<b.length;y+=1)b[y].c();u=p(),_&&_.c(),a(l,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6 animate-pulse svelte-gksjlv"),a(o,"class","lg:col-span-2 xl:col-span-3 space-y-6"),a(c,"class","space-y-6"),a(r,"class","grid gap-6 grid-cols-1 lg:grid-cols-3 xl:grid-cols-4"),a(e,"role","status"),a(e,"aria-label","Loading policy details")},m(y,S){x(y,e,S),i(e,l),i(e,n),i(e,r),i(r,o);for(let h=0;h<m.length;h+=1)m[h]&&m[h].m(o,null);i(r,s),i(r,c);for(let h=0;h<b.length;h+=1)b[h]&&b[h].m(c,null);i(e,u),_&&_.m(e,null)},p(y,S){if(S&0){d=X(Array(3));let h;for(h=0;h<d.length;h+=1){const w=ji(y,d,h);m[h]?m[h].p(w,S):(m[h]=Yi(w),m[h].c(),m[h].m(o,null))}for(;h<m.length;h+=1)m[h].d(1);m.length=d.length}if(S&0){g=X(Array(2));let h;for(h=0;h<g.length;h+=1){const w=Vi(y,g,h);b[h]?b[h].p(w,S):(b[h]=Gi(w),b[h].c(),b[h].m(c,null))}for(;h<b.length;h+=1)b[h].d(1);b.length=g.length}y[3]?_?_.p(y,S):(_=Ji(y),_.c(),_.m(e,null)):_&&(_.d(1),_=null)},d(y){y&&N(e),Ie(m,y),Ie(b,y),_&&_.d()}}}function ka(t){let e,l,n=X(Array(t[1])),r=[];for(let s=0;s<n.length;s+=1)r[s]=Ki(Ui(t,n,s));let o=t[3]&&Zi(t);return{c(){e=f("div");for(let s=0;s<r.length;s+=1)r[s].c();l=p(),o&&o.c(),a(e,"class","grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"),a(e,"role","status"),a(e,"aria-label","Loading content")},m(s,c){x(s,e,c);for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(e,null);i(e,l),o&&o.m(e,null)},p(s,c){if(c&2){n=X(Array(s[1]));let u;for(u=0;u<n.length;u+=1){const d=Ui(s,n,u);r[u]?r[u].p(d,c):(r[u]=Ki(),r[u].c(),r[u].m(e,l))}for(;u<r.length;u+=1)r[u].d(1);r.length=n.length}s[3]?o?o.p(s,c):(o=Zi(s),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},d(s){s&&N(e),Ie(r,s),o&&o.d()}}}function Fi(t){let e,l;return{c(){e=f("span"),l=v(t[2]),a(e,"class","text-sm")},m(n,r){x(n,e,r),i(e,l)},p(n,r){r&4&&T(l,n[2])},d(n){n&&N(e)}}}function $i(t){let e;return{c(){e=f("div"),e.innerHTML='<div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="w-8 h-8 bg-gray-200 rounded"></div> <div><div class="w-32 h-5 bg-gray-200 rounded mb-1"></div> <div class="w-24 h-4 bg-gray-200 rounded"></div></div></div> <div class="w-16 h-6 bg-gray-200 rounded-full"></div></div>',a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse svelte-gksjlv")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function qi(t){let e,l,n,r,o,s,c,u;return{c(){e=f("div"),l=f("div"),n=ne("svg"),r=ne("circle"),o=ne("path"),s=p(),c=f("span"),u=v(t[2]),a(r,"class","opacity-25"),a(r,"cx","12"),a(r,"cy","12"),a(r,"r","10"),a(r,"stroke","currentColor"),a(r,"stroke-width","4"),a(o,"class","opacity-75"),a(o,"fill","currentColor"),a(o,"d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"),a(n,"class","animate-spin w-5 h-5"),a(n,"fill","none"),a(n,"viewBox","0 0 24 24"),a(c,"class","text-sm font-medium"),a(l,"class","inline-flex items-center gap-2 text-gray-600"),a(e,"class","text-center py-4")},m(d,m){x(d,e,m),i(e,l),i(l,n),i(n,r),i(n,o),i(l,s),i(l,c),i(c,u)},p(d,m){m&4&&T(u,d[2])},d(d){d&&N(e)}}}function Na(t){let e;return{c(){e=f("div"),e.innerHTML='<div class="w-24 h-4 bg-gray-200 rounded"></div> <div class="w-20 h-4 bg-gray-200 rounded"></div> ',a(e,"class","flex justify-between")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function Yi(t){let e,l,n,r,o,s=X(Array(4)),c=[];for(let u=0;u<s.length;u+=1)c[u]=Na(Ea(t,s,u));return{c(){e=f("div"),l=f("div"),n=p(),r=f("div");for(let u=0;u<c.length;u+=1)c[u].c();o=p(),a(l,"class","w-32 h-6 bg-gray-200 rounded mb-4"),a(r,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse svelte-gksjlv")},m(u,d){x(u,e,d),i(e,l),i(e,n),i(e,r);for(let m=0;m<c.length;m+=1)c[m]&&c[m].m(r,null);i(e,o)},p:F,d(u){u&&N(e),Ie(c,u)}}}function xa(t){let e;return{c(){e=f("div"),a(e,"class","w-full h-4 bg-gray-200 rounded")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function Gi(t){let e,l,n,r,o,s=X(Array(3)),c=[];for(let u=0;u<s.length;u+=1)c[u]=xa(wa(t,s,u));return{c(){e=f("div"),l=f("div"),n=p(),r=f("div");for(let u=0;u<c.length;u+=1)c[u].c();o=p(),a(l,"class","w-24 h-6 bg-gray-200 rounded mb-4"),a(r,"class","space-y-3"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse svelte-gksjlv")},m(u,d){x(u,e,d),i(e,l),i(e,n),i(e,r);for(let m=0;m<c.length;m+=1)c[m]&&c[m].m(r,null);i(e,o)},p:F,d(u){u&&N(e),Ie(c,u)}}}function Ji(t){let e,l,n,r,o,s,c,u;return{c(){e=f("div"),l=f("div"),n=ne("svg"),r=ne("circle"),o=ne("path"),s=p(),c=f("span"),u=v(t[2]),a(r,"class","opacity-25"),a(r,"cx","12"),a(r,"cy","12"),a(r,"r","10"),a(r,"stroke","currentColor"),a(r,"stroke-width","4"),a(o,"class","opacity-75"),a(o,"fill","currentColor"),a(o,"d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"),a(n,"class","animate-spin w-5 h-5"),a(n,"fill","none"),a(n,"viewBox","0 0 24 24"),a(c,"class","text-sm font-medium"),a(l,"class","inline-flex items-center gap-2 text-gray-600"),a(e,"class","text-center py-8")},m(d,m){x(d,e,m),i(e,l),i(l,n),i(n,r),i(n,o),i(l,s),i(l,c),i(c,u)},p(d,m){m&4&&T(u,d[2])},d(d){d&&N(e)}}}function Ki(t){let e;return{c(){e=f("div"),e.innerHTML='<div class="flex items-center justify-between mb-3"><div class="flex items-center gap-2"><div class="w-8 h-8 bg-gray-200 rounded"></div> <div class="w-16 h-5 bg-gray-200 rounded"></div></div> <div class="w-16 h-6 bg-gray-200 rounded-full"></div></div> <div class="w-32 h-4 bg-gray-200 rounded mb-4"></div> <div class="space-y-3 mb-4"><div class="flex justify-between"><div class="w-24 h-4 bg-gray-200 rounded"></div> <div class="w-20 h-4 bg-gray-200 rounded"></div></div> <div class="flex justify-between"><div class="w-28 h-4 bg-gray-200 rounded"></div> <div class="w-16 h-4 bg-gray-200 rounded"></div></div> <div class="flex justify-between"><div class="w-16 h-4 bg-gray-200 rounded"></div> <div class="w-20 h-4 bg-gray-200 rounded"></div></div></div> <div class="border-t border-gray-100 pt-4"><div class="space-y-2"><div class="w-full h-3 bg-gray-200 rounded"></div> <div class="w-3/4 h-3 bg-gray-200 rounded"></div></div></div>',a(e,"class","bg-white rounded-lg shadow-md border border-gray-100 min-h-[280px] p-6 animate-pulse svelte-gksjlv")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function Zi(t){let e,l,n,r,o,s,c,u;return{c(){e=f("div"),l=f("div"),n=ne("svg"),r=ne("circle"),o=ne("path"),s=p(),c=f("span"),u=v(t[2]),a(r,"class","opacity-25"),a(r,"cx","12"),a(r,"cy","12"),a(r,"r","10"),a(r,"stroke","currentColor"),a(r,"stroke-width","4"),a(o,"class","opacity-75"),a(o,"fill","currentColor"),a(o,"d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"),a(n,"class","animate-spin w-5 h-5"),a(n,"fill","none"),a(n,"viewBox","0 0 24 24"),a(c,"class","text-sm font-medium"),a(l,"class","inline-flex items-center gap-2 text-gray-600"),a(e,"class","col-span-full text-center py-4")},m(d,m){x(d,e,m),i(e,l),i(l,n),i(n,r),i(n,o),i(l,s),i(l,c),i(c,u)},p(d,m){m&4&&T(u,d[2])},d(d){d&&N(e)}}}function Ma(t){let e;function l(o,s){if(o[0]==="cards")return ka;if(o[0]==="detail")return Ia;if(o[0]==="list")return Ta;if(o[0]==="inline")return Ca}let n=l(t),r=n&&n(t);return{c(){r&&r.c(),e=kl()},m(o,s){r&&r.m(o,s),x(o,e,s)},p(o,[s]){n===(n=l(o))&&r?r.p(o,s):(r&&r.d(1),r=n&&n(o),r&&(r.c(),r.m(e.parentNode,e)))},i:F,o:F,d(o){o&&N(e),r&&r.d(o)}}}function Sa(t,e,l){let{variant:n="cards"}=e,{count:r=3}=e,{message:o="Loading..."}=e,{showMessage:s=!0}=e;return t.$$set=c=>{"variant"in c&&l(0,n=c.variant),"count"in c&&l(1,r=c.count),"message"in c&&l(2,o=c.message),"showMessage"in c&&l(3,s=c.showMessage)},[n,r,o,s]}class us extends Mt{constructor(e){super(),xt(this,e,Sa,Ma,pt,{variant:0,count:1,message:2,showMessage:3})}}function Pa(t){let e;return{c(){e=v("Something Went Wrong")},m(l,n){x(l,e,n)},d(l){l&&N(e)}}}function Da(t){let e;return{c(){e=v("Not Found")},m(l,n){x(l,e,n)},d(l){l&&N(e)}}}function Aa(t){let e;return{c(){e=v("Invalid Information")},m(l,n){x(l,e,n)},d(l){l&&N(e)}}}function Ra(t){let e;return{c(){e=v("Service Unavailable")},m(l,n){x(l,e,n)},d(l){l&&N(e)}}}function Oa(t){let e;return{c(){e=v("Connection Problem")},m(l,n){x(l,e,n)},d(l){l&&N(e)}}}function Wi(t){let e,l,n,r,o,s,c;return{c(){e=f("button"),l=ne("svg"),n=ne("path"),r=p(),o=v(t[4]),a(n,"stroke-linecap","round"),a(n,"stroke-linejoin","round"),a(n,"stroke-width","2"),a(n,"d","M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"),a(l,"class","mr-2 w-4 h-4"),a(l,"fill","none"),a(l,"stroke","currentColor"),a(l,"viewBox","0 0 24 24"),a(e,"class","inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(e,"aria-label","Retry the failed operation")},m(u,d){x(u,e,d),i(e,l),i(l,n),i(e,r),i(e,o),s||(c=ie(e,"click",t[7]),s=!0)},p(u,d){d&16&&T(o,u[4])},d(u){u&&N(e),s=!1,c()}}}function Qi(t){let e,l,n;return{c(){e=f("button"),e.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        Go Back`,a(e,"class","inline-flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"),a(e,"aria-label","Go back to previous page")},m(r,o){x(r,e,o),l||(n=ie(e,"click",t[10]),l=!0)},p:F,d(r){r&&N(e),l=!1,n()}}}function Xi(t){let e,l=t[0]?"Hide Details":"Show Details",n,r,o;return{c(){e=f("button"),n=v(l),a(e,"class","text-gray-500 hover:text-gray-700 text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 rounded"),a(e,"aria-label","Toggle error details")},m(s,c){x(s,e,c),i(e,n),r||(o=ie(e,"click",t[8]),r=!0)},p(s,c){c&1&&l!==(l=s[0]?"Hide Details":"Show Details")&&T(n,l)},d(s){s&&N(e),r=!1,o()}}}function er(t){let e,l,n,r,o,s,c,u=t[2].status&&tr(t),d=t[2].code&&lr(t),m=t[2].message&&t[2].message!==t[5]&&nr(t),g=t[2].timestamp&&ir(t);return{c(){e=f("div"),l=f("h3"),l.textContent="Error Details",n=p(),r=f("div"),u&&u.c(),o=p(),d&&d.c(),s=p(),m&&m.c(),c=p(),g&&g.c(),a(l,"class","text-sm font-semibold text-gray-900 mb-2"),a(r,"class","text-left space-y-2"),a(e,"class","mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200 max-w-2xl w-full")},m(b,_){x(b,e,_),i(e,l),i(e,n),i(e,r),u&&u.m(r,null),i(r,o),d&&d.m(r,null),i(r,s),m&&m.m(r,null),i(r,c),g&&g.m(r,null)},p(b,_){b[2].status?u?u.p(b,_):(u=tr(b),u.c(),u.m(r,o)):u&&(u.d(1),u=null),b[2].code?d?d.p(b,_):(d=lr(b),d.c(),d.m(r,s)):d&&(d.d(1),d=null),b[2].message&&b[2].message!==b[5]?m?m.p(b,_):(m=nr(b),m.c(),m.m(r,c)):m&&(m.d(1),m=null),b[2].timestamp?g?g.p(b,_):(g=ir(b),g.c(),g.m(r,null)):g&&(g.d(1),g=null)},d(b){b&&N(e),u&&u.d(),d&&d.d(),m&&m.d(),g&&g.d()}}}function tr(t){let e,l,n,r,o=t[2].status+"",s;return{c(){e=f("div"),l=f("span"),l.textContent="Status:",n=p(),r=f("span"),s=v(o),a(l,"class","font-medium text-gray-700"),a(r,"class","text-gray-600 font-mono"),a(e,"class","text-xs")},m(c,u){x(c,e,u),i(e,l),i(e,n),i(e,r),i(r,s)},p(c,u){u&4&&o!==(o=c[2].status+"")&&T(s,o)},d(c){c&&N(e)}}}function lr(t){let e,l,n,r,o=t[2].code+"",s;return{c(){e=f("div"),l=f("span"),l.textContent="Code:",n=p(),r=f("span"),s=v(o),a(l,"class","font-medium text-gray-700"),a(r,"class","text-gray-600 font-mono"),a(e,"class","text-xs")},m(c,u){x(c,e,u),i(e,l),i(e,n),i(e,r),i(r,s)},p(c,u){u&4&&o!==(o=c[2].code+"")&&T(s,o)},d(c){c&&N(e)}}}function nr(t){let e,l,n,r,o=t[2].message+"",s;return{c(){e=f("div"),l=f("span"),l.textContent="Technical Message:",n=p(),r=f("span"),s=v(o),a(l,"class","font-medium text-gray-700"),a(r,"class","text-gray-600 font-mono break-words"),a(e,"class","text-xs")},m(c,u){x(c,e,u),i(e,l),i(e,n),i(e,r),i(r,s)},p(c,u){u&4&&o!==(o=c[2].message+"")&&T(s,o)},d(c){c&&N(e)}}}function ir(t){let e,l,n,r,o=new Date(t[2].timestamp).toLocaleString()+"",s;return{c(){e=f("div"),l=f("span"),l.textContent="Time:",n=p(),r=f("span"),s=v(o),a(l,"class","font-medium text-gray-700"),a(r,"class","text-gray-600 font-mono"),a(e,"class","text-xs")},m(c,u){x(c,e,u),i(e,l),i(e,n),i(e,r),i(r,s)},p(c,u){u&4&&o!==(o=new Date(c[2].timestamp).toLocaleString()+"")&&T(s,o)},d(c){c&&N(e)}}}function rr(t){let e,l,n,r,o,s,c,u=t[3]&&sr(t);return{c(){e=f("div"),l=ne("svg"),n=ne("path"),r=p(),o=f("span"),s=v(t[5]),c=p(),u&&u.c(),a(n,"fill-rule","evenodd"),a(n,"d","M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"),a(n,"clip-rule","evenodd"),a(l,"class","w-5 h-5 flex-shrink-0"),a(l,"fill","currentColor"),a(l,"viewBox","0 0 20 20"),a(o,"class","text-sm font-medium"),a(e,"class","flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800"),a(e,"role","alert")},m(d,m){x(d,e,m),i(e,l),i(l,n),i(e,r),i(e,o),i(o,s),i(e,c),u&&u.m(e,null)},p(d,m){m&32&&T(s,d[5]),d[3]?u?u.p(d,m):(u=sr(d),u.c(),u.m(e,null)):u&&(u.d(1),u=null)},d(d){d&&N(e),u&&u.d()}}}function sr(t){let e,l,n;return{c(){e=f("button"),e.textContent="Retry",a(e,"class","ml-auto text-red-600 hover:text-red-800 text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded"),a(e,"aria-label","Retry the failed operation")},m(r,o){x(r,e,o),l||(n=ie(e,"click",t[7]),l=!0)},p:F,d(r){r&&N(e),l=!1,n()}}}function ar(t){let e,l,n,r,o,s,c,u,d,m,g,b,_,y,S,h,w=t[3]&&or(t);return{c(){e=f("div"),l=f("div"),n=f("div"),r=f("div"),r.innerHTML='<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',o=p(),s=f("div"),c=f("p"),c.textContent="Error",u=p(),d=f("p"),m=v(t[5]),g=p(),w&&w.c(),b=p(),_=f("div"),y=f("button"),y.innerHTML='<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>',a(r,"class","flex-shrink-0"),a(c,"class","text-sm font-medium text-gray-900"),a(d,"class","mt-1 text-sm text-gray-500"),a(s,"class","ml-3 w-0 flex-1"),a(y,"class","bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"),a(y,"aria-label","Dismiss error"),a(_,"class","ml-4 flex-shrink-0 flex"),a(n,"class","flex items-start"),a(l,"class","p-4"),a(e,"class","fixed top-4 right-4 z-50 max-w-sm w-full bg-white border border-red-200 rounded-lg shadow-lg"),a(e,"role","alert")},m(A,R){x(A,e,R),i(e,l),i(l,n),i(n,r),i(n,o),i(n,s),i(s,c),i(s,u),i(s,d),i(d,m),i(s,g),w&&w.m(s,null),i(n,b),i(n,_),i(_,y),S||(h=ie(y,"click",t[11]),S=!0)},p(A,R){R&32&&T(m,A[5]),A[3]?w?w.p(A,R):(w=or(A),w.c(),w.m(s,null)):w&&(w.d(1),w=null)},d(A){A&&N(e),w&&w.d(),S=!1,h()}}}function or(t){let e,l,n,r,o;return{c(){e=f("div"),l=f("button"),n=v(t[4]),a(l,"class","text-sm font-medium text-red-600 hover:text-red-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded"),a(e,"class","mt-3")},m(s,c){x(s,e,c),i(e,l),i(l,n),r||(o=ie(l,"click",t[7]),r=!0)},p(s,c){c&16&&T(n,s[4])},d(s){s&&N(e),r=!1,o()}}}function Ha(t){let e,l,n=cr(t[1])+"",r,o,s,c,u,d,m,g,b,_,y,S,h,w;function A(D,M){return D[1]==="network"?Oa:D[1]==="api"?Ra:D[1]==="validation"?Aa:D[1]==="not-found"?Da:Pa}let R=A(t),z=R(t),I=t[3]&&Wi(t),E=t[1]==="not-found"&&Qi(t),P=t[2]&&(t[2].status||t[2].stack)&&Xi(t),C=t[0]&&t[2]&&er(t),k=t[1]==="inline"&&rr(t),O=t[1]==="toast"&&ar(t);return{c(){e=f("div"),l=f("div"),r=v(n),o=p(),s=f("h2"),z.c(),c=p(),u=f("p"),d=v(t[5]),m=p(),g=f("div"),I&&I.c(),b=p(),E&&E.c(),_=p(),P&&P.c(),y=p(),C&&C.c(),S=p(),k&&k.c(),h=p(),O&&O.c(),w=kl(),a(l,"class","text-6xl mb-4"),a(l,"aria-hidden","true"),a(s,"class","text-2xl font-bold text-gray-900 mb-2"),a(u,"class","text-gray-600 max-w-md mb-6 leading-relaxed"),a(g,"class","flex flex-col sm:flex-row gap-3 items-center"),a(e,"class","flex flex-col items-center justify-center py-12 px-4 text-center"),a(e,"role","alert"),a(e,"aria-live","polite")},m(D,M){x(D,e,M),i(e,l),i(l,r),i(e,o),i(e,s),z.m(s,null),i(e,c),i(e,u),i(u,d),i(e,m),i(e,g),I&&I.m(g,null),i(g,b),E&&E.m(g,null),i(g,_),P&&P.m(g,null),i(e,y),C&&C.m(e,null),x(D,S,M),k&&k.m(D,M),x(D,h,M),O&&O.m(D,M),x(D,w,M)},p(D,[M]){M&2&&n!==(n=cr(D[1])+"")&&T(r,n),R!==(R=A(D))&&(z.d(1),z=R(D),z&&(z.c(),z.m(s,null))),M&32&&T(d,D[5]),D[3]?I?I.p(D,M):(I=Wi(D),I.c(),I.m(g,b)):I&&(I.d(1),I=null),D[1]==="not-found"?E?E.p(D,M):(E=Qi(D),E.c(),E.m(g,_)):E&&(E.d(1),E=null),D[2]&&(D[2].status||D[2].stack)?P?P.p(D,M):(P=Xi(D),P.c(),P.m(g,null)):P&&(P.d(1),P=null),D[0]&&D[2]?C?C.p(D,M):(C=er(D),C.c(),C.m(e,null)):C&&(C.d(1),C=null),D[1]==="inline"?k?k.p(D,M):(k=rr(D),k.c(),k.m(h.parentNode,h)):k&&(k.d(1),k=null),D[1]==="toast"?O?O.p(D,M):(O=ar(D),O.c(),O.m(w.parentNode,w)):O&&(O.d(1),O=null)},i:F,o:F,d(D){D&&(N(e),N(S),N(h),N(w)),z.d(),I&&I.d(),E&&E.d(),P&&P.d(),C&&C.d(),k&&k.d(D),O&&O.d(D)}}}function cr(t){switch(t){case"network":return"🌐";case"api":return"⚠️";case"validation":return"❌";case"not-found":return"🔍";default:return"⚠️"}}function La(t,e,l){let n,{variant:r="generic"}=e,{message:o=""}=e,{error:s=null}=e,{showRetry:c=!0}=e,{retryText:u="Try Again"}=e,{showDetails:d=!1}=e;const m=nn(),g={network:"Unable to connect to the server. Please check your internet connection and try again.",api:"We're experiencing technical difficulties. Please try again in a moment.",validation:"There was an issue with the provided information. Please check your input and try again.","not-found":"The requested information could not be found.",generic:"Something went wrong. Please try again."};function b(){m("retry")}function _(){l(0,d=!d)}const y=()=>m("goBack"),S=()=>m("dismiss");return t.$$set=h=>{"variant"in h&&l(1,r=h.variant),"message"in h&&l(9,o=h.message),"error"in h&&l(2,s=h.error),"showRetry"in h&&l(3,c=h.showRetry),"retryText"in h&&l(4,u=h.retryText),"showDetails"in h&&l(0,d=h.showDetails)},t.$$.update=()=>{var h;t.$$.dirty&518&&l(5,n=o||((h=s==null?void 0:s.getUserMessage)==null?void 0:h.call(s))||(s==null?void 0:s.message)||g[r]||g.generic)},[d,r,s,c,u,n,m,b,_,o,y,S]}class fn extends Mt{constructor(e){super(),xt(this,e,La,Ha,pt,{variant:1,message:9,error:2,showRetry:3,retryText:4,showDetails:0})}}function ur(t,e,l){const n=t.slice();return n[26]=e[l],n}function dr(t,e,l){const n=t.slice();return n[26]=e[l],n}function fr(t){let e;return{c(){e=f("label"),e.textContent="เลือกสมาชิก",a(e,"for","citizen-selector"),a(e,"class","block text-sm font-medium text-gray-700 mb-1")},m(l,n){x(l,e,n)},d(l){l&&N(e)}}}function za(t){let e;return{c(){e=f("span"),e.textContent="เลือกสมาชิก",a(e,"class","text-gray-500")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function Ba(t){let e,l=t[3].displayName+"",n,r,o,s,c=t[3].citizenID+"",u,d;return{c(){e=f("span"),n=v(l),r=p(),o=f("span"),s=v("("),u=v(c),d=v(")"),a(e,"class","truncate"),a(o,"class","text-xs text-gray-500")},m(m,g){x(m,e,g),i(e,n),x(m,r,g),x(m,o,g),i(o,s),i(o,u),i(o,d)},p(m,g){g&8&&l!==(l=m[3].displayName+"")&&T(n,l),g&8&&c!==(c=m[3].citizenID+"")&&T(u,c)},d(m){m&&(N(e),N(r),N(o))}}}function mr(t){let e,l,n=X(t[8]),r=[];for(let o=0;o<n.length;o+=1)r[o]=pr(dr(t,n,o));return{c(){e=f("div"),l=f("div");for(let o=0;o<r.length;o+=1)r[o].c();a(l,"class","py-1"),a(e,"id","citizen-listbox"),a(e,"class","absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"),a(e,"role","listbox"),a(e,"aria-label","Citizen list")},m(o,s){x(o,e,s),i(e,l);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(l,null)},p(o,s){if(s&2312){n=X(o[8]);let c;for(c=0;c<n.length;c+=1){const u=dr(o,n,c);r[c]?r[c].p(u,s):(r[c]=pr(u),r[c].c(),r[c].m(l,null))}for(;c<r.length;c+=1)r[c].d(1);r.length=n.length}},d(o){o&&N(e),Ie(r,o)}}}function pr(t){let e,l,n,r=t[26].label+"",o,s,c,u=t[26].value+"",d,m,g,b,_,y;function S(){return t[18](t[26])}return{c(){var h,w;e=f("button"),l=f("div"),n=f("span"),o=v(r),s=p(),c=f("span"),d=v(u),m=p(),a(n,"class","font-medium"),a(c,"class","text-xs text-gray-500"),a(l,"class","flex flex-col min-w-0 flex-1"),a(e,"type","button"),a(e,"class",g="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none flex items-center justify-between "+(t[26].value===((h=t[3])==null?void 0:h.citizenID)?"bg-blue-50 text-blue-700":"text-gray-900")),a(e,"role","option"),a(e,"aria-selected",b=t[26].value===((w=t[3])==null?void 0:w.citizenID))},m(h,w){x(h,e,w),i(e,l),i(l,n),i(n,o),i(l,s),i(l,c),i(c,d),i(e,m),_||(y=ie(e,"click",S),_=!0)},p(h,w){var A,R;t=h,w&256&&r!==(r=t[26].label+"")&&T(o,r),w&256&&u!==(u=t[26].value+"")&&T(d,u),w&264&&g!==(g="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none flex items-center justify-between "+(t[26].value===((A=t[3])==null?void 0:A.citizenID)?"bg-blue-50 text-blue-700":"text-gray-900"))&&a(e,"class",g),w&264&&b!==(b=t[26].value===((R=t[3])==null?void 0:R.citizenID))&&a(e,"aria-selected",b)},d(h){h&&N(e),_=!1,y()}}}function gr(t){let e;return{c(){e=f("label"),e.textContent="เลือกบริษัทประกัน",a(e,"for","insurer-selector"),a(e,"class","block text-sm font-medium text-gray-700 mb-1")},m(l,n){x(l,e,n)},d(l){l&&N(e)}}}function Va(t){let e;return{c(){e=f("span"),e.textContent="เลือกสมาชิกก่อน",a(e,"class","text-gray-400")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function ja(t){let e;return{c(){e=f("span"),e.textContent="เลือกบริษัทประกัน",a(e,"class","text-gray-500")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function Ua(t){let e,l=t[10].displayName+"",n,r,o,s,c=t[10].insurerCode+"",u,d;return{c(){e=f("span"),n=v(l),r=p(),o=f("span"),s=v("("),u=v(c),d=v(")"),a(e,"class","truncate"),a(o,"class","text-xs text-gray-500")},m(m,g){x(m,e,g),i(e,n),x(m,r,g),x(m,o,g),i(o,s),i(o,u),i(o,d)},p(m,g){g&1024&&l!==(l=m[10].displayName+"")&&T(n,l),g&1024&&c!==(c=m[10].insurerCode+"")&&T(u,c)},d(m){m&&(N(e),N(r),N(o))}}}function br(t){let e,l;function n(s,c){return s[9].length===0?$a:Fa}let r=n(t),o=r(t);return{c(){e=f("div"),l=f("div"),o.c(),a(l,"class","py-1"),a(e,"id","insurer-listbox"),a(e,"class","absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"),a(e,"role","listbox"),a(e,"aria-label","Insurer list")},m(s,c){x(s,e,c),i(e,l),o.m(l,null)},p(s,c){r===(r=n(s))&&o?o.p(s,c):(o.d(1),o=r(s),o&&(o.c(),o.m(l,null)))},d(s){s&&N(e),o.d()}}}function Fa(t){let e,l=X(t[9]),n=[];for(let r=0;r<l.length;r+=1)n[r]=hr(ur(t,l,r));return{c(){for(let r=0;r<n.length;r+=1)n[r].c();e=kl()},m(r,o){for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(r,o);x(r,e,o)},p(r,o){if(o&5632){l=X(r[9]);let s;for(s=0;s<l.length;s+=1){const c=ur(r,l,s);n[s]?n[s].p(c,o):(n[s]=hr(c),n[s].c(),n[s].m(e.parentNode,e))}for(;s<n.length;s+=1)n[s].d(1);n.length=l.length}},d(r){r&&N(e),Ie(n,r)}}}function $a(t){let e;return{c(){e=f("div"),e.textContent="ไม่พบบริษัทประกัน",a(e,"class","px-3 py-2 text-sm text-gray-500 text-center")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function hr(t){let e,l,n,r=t[26].label+"",o,s,c,u=t[26].value+"",d,m,g,b,_,y;function S(){return t[20](t[26])}return{c(){var h,w;e=f("button"),l=f("div"),n=f("span"),o=v(r),s=p(),c=f("span"),d=v(u),m=p(),a(n,"class","font-medium"),a(c,"class","text-xs text-gray-500"),a(l,"class","flex flex-col min-w-0 flex-1"),a(e,"type","button"),a(e,"class",g="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none flex items-center justify-between "+(t[26].value===((h=t[10])==null?void 0:h.insurerCode)?"bg-blue-50 text-blue-700":"text-gray-900")),a(e,"role","option"),a(e,"aria-selected",b=t[26].value===((w=t[10])==null?void 0:w.insurerCode))},m(h,w){x(h,e,w),i(e,l),i(l,n),i(n,o),i(l,s),i(l,c),i(c,d),i(e,m),_||(y=ie(e,"click",S),_=!0)},p(h,w){var A,R;t=h,w&512&&r!==(r=t[26].label+"")&&T(o,r),w&512&&u!==(u=t[26].value+"")&&T(d,u),w&1536&&g!==(g="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none flex items-center justify-between "+(t[26].value===((A=t[10])==null?void 0:A.insurerCode)?"bg-blue-50 text-blue-700":"text-gray-900"))&&a(e,"class",g),w&1536&&b!==(b=t[26].value===((R=t[10])==null?void 0:R.insurerCode))&&a(e,"aria-selected",b)},d(h){h&&N(e),_=!1,y()}}}function qa(t){let e,l,n,r,o,s,c,u,d,m,g,b,_,y,S,h,w,A,R,z,I,E,P,C,k,O,D,M,H,L,j,$=t[1]&&fr();function V(B,Y){return B[3]?Ba:za}let q=V(t),J=q(t),W=t[4]&&mr(t),K=t[1]&&gr();function re(B,Y){return B[10]?Ua:B[3]?ja:Va}let se=re(t),Q=se(t),G=t[5]&&t[3]&&br(t);return{c(){var B,Y;e=f("div"),l=f("div"),$&&$.c(),n=p(),r=f("div"),o=f("button"),s=f("div"),J.c(),c=p(),u=ne("svg"),d=ne("path"),b=p(),W&&W.c(),y=p(),S=f("div"),K&&K.c(),h=p(),w=f("div"),A=f("button"),R=f("div"),Q.c(),z=p(),I=ne("svg"),E=ne("path"),D=p(),G&&G.c(),a(s,"class","flex items-center space-x-2 min-w-0 flex-1"),a(d,"stroke-linecap","round"),a(d,"stroke-linejoin","round"),a(d,"stroke-width","2"),a(d,"d","M19 9l-7 7-7-7"),a(u,"class",m="w-4 h-4 text-gray-400 transition-transform duration-200 "+(t[4]?"transform rotate-180":"")),a(u,"fill","none"),a(u,"stroke","currentColor"),a(u,"viewBox","0 0 24 24"),a(u,"aria-hidden","true"),a(o,"id","citizen-selector"),a(o,"type","button"),a(o,"class","inline-flex items-center justify-between w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"),a(o,"aria-label",g="Select citizen: "+(((B=t[3])==null?void 0:B.displayName)||"ไม่ได้เลือกสมาชิก")),a(r,"class","relative"),a(r,"role","combobox"),a(r,"aria-expanded",t[4]),a(r,"aria-haspopup","listbox"),a(r,"aria-controls","citizen-listbox"),a(r,"aria-label","Select citizen"),a(l,"class",_="relative "+(t[2]?"flex-1 min-w-0":"")),a(R,"class","flex items-center space-x-2 min-w-0 flex-1"),a(E,"stroke-linecap","round"),a(E,"stroke-linejoin","round"),a(E,"stroke-width","2"),a(E,"d","M19 9l-7 7-7-7"),a(I,"class",P="w-4 h-4 text-gray-400 transition-transform duration-200 "+(t[5]?"transform rotate-180":"")),a(I,"fill","none"),a(I,"stroke","currentColor"),a(I,"viewBox","0 0 24 24"),a(I,"aria-hidden","true"),a(A,"id","insurer-selector"),a(A,"type","button"),a(A,"class",C="inline-flex items-center justify-between w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 "+(t[3]?"":"opacity-50 cursor-not-allowed")),A.disabled=k=!t[3],a(A,"aria-label",O="Select insurer: "+(((Y=t[10])==null?void 0:Y.displayName)||"ไม่ได้เลือกบริษัทประกัน")),a(w,"class","relative"),a(w,"role","combobox"),a(w,"aria-expanded",t[5]),a(w,"aria-haspopup","listbox"),a(w,"aria-controls","insurer-listbox"),a(w,"aria-label","Select insurer"),a(S,"class",M="relative "+(t[2]?"flex-1 min-w-0":"")),a(e,"class",H=(t[2]?"flex flex-col sm:flex-row sm:space-x-2 sm:space-y-0 space-y-3":"flex flex-col space-y-3")+" "+(t[0]?"min-w-[200px]":"min-w-[280px]"))},m(B,Y){x(B,e,Y),i(e,l),$&&$.m(l,null),i(l,n),i(l,r),i(r,o),i(o,s),J.m(s,null),i(o,c),i(o,u),i(u,d),i(r,b),W&&W.m(r,null),t[19](r),i(e,y),i(e,S),K&&K.m(S,null),i(S,h),i(S,w),i(w,A),i(A,R),Q.m(R,null),i(A,z),i(A,I),i(I,E),i(w,D),G&&G.m(w,null),t[21](w),L||(j=[ie(o,"click",t[13]),ie(A,"click",t[14])],L=!0)},p(B,[Y]){var ae,de;B[1]?$||($=fr(),$.c(),$.m(l,n)):$&&($.d(1),$=null),q===(q=V(B))&&J?J.p(B,Y):(J.d(1),J=q(B),J&&(J.c(),J.m(s,null))),Y&16&&m!==(m="w-4 h-4 text-gray-400 transition-transform duration-200 "+(B[4]?"transform rotate-180":""))&&a(u,"class",m),Y&8&&g!==(g="Select citizen: "+(((ae=B[3])==null?void 0:ae.displayName)||"ไม่ได้เลือกสมาชิก"))&&a(o,"aria-label",g),B[4]?W?W.p(B,Y):(W=mr(B),W.c(),W.m(r,null)):W&&(W.d(1),W=null),Y&16&&a(r,"aria-expanded",B[4]),Y&4&&_!==(_="relative "+(B[2]?"flex-1 min-w-0":""))&&a(l,"class",_),B[1]?K||(K=gr(),K.c(),K.m(S,h)):K&&(K.d(1),K=null),se===(se=re(B))&&Q?Q.p(B,Y):(Q.d(1),Q=se(B),Q&&(Q.c(),Q.m(R,null))),Y&32&&P!==(P="w-4 h-4 text-gray-400 transition-transform duration-200 "+(B[5]?"transform rotate-180":""))&&a(I,"class",P),Y&8&&C!==(C="inline-flex items-center justify-between w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 "+(B[3]?"":"opacity-50 cursor-not-allowed"))&&a(A,"class",C),Y&8&&k!==(k=!B[3])&&(A.disabled=k),Y&1024&&O!==(O="Select insurer: "+(((de=B[10])==null?void 0:de.displayName)||"ไม่ได้เลือกบริษัทประกัน"))&&a(A,"aria-label",O),B[5]&&B[3]?G?G.p(B,Y):(G=br(B),G.c(),G.m(w,null)):G&&(G.d(1),G=null),Y&32&&a(w,"aria-expanded",B[5]),Y&4&&M!==(M="relative "+(B[2]?"flex-1 min-w-0":""))&&a(S,"class",M),Y&5&&H!==(H=(B[2]?"flex flex-col sm:flex-row sm:space-x-2 sm:space-y-0 space-y-3":"flex flex-col space-y-3")+" "+(B[0]?"min-w-[200px]":"min-w-[280px]"))&&a(e,"class",H)},i:F,o:F,d(B){B&&N(e),$&&$.d(),J.d(),W&&W.d(),t[19](null),K&&K.d(),Q.d(),G&&G.d(),t[21](null),L=!1,Je(j)}}}function Ya(t,e,l){let n,r,o,s,c;Te(t,me,M=>l(15,o=M)),Te(t,Dt,M=>l(16,s=M)),Te(t,Pt,M=>l(17,c=M));let{compact:u=!1}=e,{showLabels:d=!0}=e,{horizontal:m=!1}=e,g=!1,b=!1,_=null,y=null,S=[],h=[];Cl(()=>{l(8,S=ra())});function w(M){pa(M.value,M.citizen),z()}function A(M){ga(M.value,M.insurer),E()}function R(){l(4,g=!0),l(5,b=!1)}function z(){l(4,g=!1)}function I(){n&&(l(5,b=!0),l(4,g=!1))}function E(){l(5,b=!1)}function P(M){_&&!_.contains(M.target)&&z(),y&&!y.contains(M.target)&&E()}Cl(()=>(document.addEventListener("click",P),()=>{document.removeEventListener("click",P)}));const C=M=>w(M);function k(M){Tl[M?"unshift":"push"](()=>{_=M,l(6,_)})}const O=M=>A(M);function D(M){Tl[M?"unshift":"push"](()=>{y=M,l(7,y)})}return t.$$set=M=>{"compact"in M&&l(0,u=M.compact),"showLabels"in M&&l(1,d=M.showLabels),"horizontal"in M&&l(2,m=M.horizontal)},t.$$.update=()=>{t.$$.dirty&131072&&l(3,n=c),t.$$.dirty&65536&&l(10,r=s),t.$$.dirty&32768,t.$$.dirty&8&&(n?l(9,h=sa(n.citizenID)):l(9,h=[]))},[u,d,m,n,g,b,_,y,S,h,r,w,A,R,I,o,s,c,C,k,O,D]}class Ga extends Mt{constructor(e){super(),xt(this,e,Ya,qa,pt,{compact:0,showLabels:1,horizontal:2})}}function _r(t,e,l){const n=t.slice();return n[21]=e[l],n}function Ja(t){let e;return{c(){e=f("p"),e.textContent="กรุณาเลือกสมาชิกและบริษัทประกันเพื่อดูข้อมูลกรมธรรม์",a(e,"class","text-gray-600")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function Ka(t){let e;return{c(){e=f("p"),e.textContent="กำลังโหลดข้อมูลสมาชิก...",a(e,"class","text-gray-600")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function Za(t){let e,l,n,r,o,s,c,u=t[0].memberCode+"",d,m;return{c(){e=f("p"),l=v("กรมธรรม์ของ "),n=f("span"),r=v(t[4]),o=p(),s=f("span"),c=v("("),d=v(u),m=v(")"),a(n,"class","font-semibold text-gray-800"),a(s,"class","text-sm text-gray-500 ml-2"),a(e,"class","text-gray-600")},m(g,b){x(g,e,b),i(e,l),i(e,n),i(n,r),i(e,o),i(e,s),i(s,c),i(s,d),i(s,m)},p(g,b){b&16&&T(r,g[4]),b&1&&u!==(u=g[0].memberCode+"")&&T(d,u)},d(g){g&&N(e)}}}function Wa(t){let e,l=[],n=new Map,r=X(t[7]);const o=s=>s[21].MemberCode||s[21].PolicyNo;for(let s=0;s<r.length;s+=1){let c=_r(t,r,s),u=o(c);n.set(u,l[s]=wr(u,c))}return{c(){e=f("section");for(let s=0;s<l.length;s+=1)l[s].c();a(e,"class","grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3"),a(e,"aria-label","Policy cards grid")},m(s,c){x(s,e,c);for(let u=0;u<l.length;u+=1)l[u]&&l[u].m(e,null)},p(s,c){c&1920&&(r=X(s[7]),l=Es(l,c,o,1,s,r,n,e,ws,wr,null,_r))},i:F,o:F,d(s){s&&N(e);for(let c=0;c<l.length;c+=1)l[c].d()}}}function Qa(t){let e,l,n,r,o,s,c,u,d,m;return{c(){e=f("div"),l=f("div"),l.textContent="📄",n=p(),r=f("h2"),r.textContent="ไม่พบข้อมูลกรมธรรม์",o=p(),s=f("p"),s.textContent="ไม่พบกรมธรรม์ประกันภัยสำหรับสมาชิกท่านนี้",c=p(),u=f("button"),u.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>
          รีเฟรช`,a(l,"class","text-6xl mb-4"),a(l,"aria-hidden","true"),a(r,"class","text-2xl font-bold text-gray-900 mb-2"),a(s,"class","text-gray-600 mb-6"),a(u,"class","inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(u,"aria-label","Refresh policies"),a(e,"class","text-center py-12")},m(g,b){x(g,e,b),i(e,l),i(e,n),i(e,r),i(e,o),i(e,s),i(e,c),i(e,u),d||(m=ie(u,"click",t[11]),d=!0)},p:F,i:F,o:F,d(g){g&&N(e),d=!1,m()}}}function Xa(t){let e,l;return e=new fn({props:{variant:"api",error:t[5],message:"ไม่สามารถโหลดข้อมูลกรมธรรม์ได้ กรุณาลองใหม่อีกครั้ง"}}),e.$on("retry",t[11]),{c(){Ke(e.$$.fragment)},m(n,r){Le(e,n,r),l=!0},p(n,r){const o={};r&32&&(o.error=n[5]),e.$set(o)},i(n){l||(ue(e.$$.fragment,n),l=!0)},o(n){be(e.$$.fragment,n),l=!1},d(n){ze(e,n)}}}function eo(t){let e,l;return e=new us({props:{variant:"cards",count:6,message:"กำลังโหลดข้อมูลกรมธรรม์..."}}),{c(){Ke(e.$$.fragment)},m(n,r){Le(e,n,r),l=!0},p:F,i(n){l||(ue(e.$$.fragment,n),l=!0)},o(n){be(e.$$.fragment,n),l=!1},d(n){ze(e,n)}}}function to(t){let e;return{c(){e=f("div"),e.innerHTML='<div class="text-6xl mb-4" aria-hidden="true">👤</div> <h2 class="text-2xl font-bold text-gray-900 mb-2">กรุณาเลือกข้อมูลสมาชิก</h2> <p class="text-gray-600 mb-6">กรุณาเลือกสมาชิกและบริษัทประกันจากด้านบนเพื่อดูข้อมูลกรมธรรม์</p>',a(e,"class","text-center py-12")},m(l,n){x(l,e,n)},p:F,i:F,o:F,d(l){l&&N(e)}}}function yr(t){let e;return{c(){e=f("span"),e.textContent="VIP",a(e,"class","px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200")},m(l,n){x(l,e,n)},d(l){l&&N(e)}}}function vr(t){let e,l=t[21].CompanyName+"",n;return{c(){e=f("p"),n=v(l),a(e,"class","text-xs text-gray-500 mt-1")},m(r,o){x(r,e,o),i(e,n)},p(r,o){o&128&&l!==(l=r[21].CompanyName+"")&&T(n,l)},d(r){r&&N(e)}}}function wr(t,e){let l,n,r,o,s,c=e[9](e[21].PlanName)+"",u,d,m=en(e[21].PlanName)+"",g,b,_,y,S,h=e[21].MemberStatus+"",w,A,R,z,I,E,P,C=e[21].PolicyNo+"",k,O,D,M,H=e[21].CertificateNo+"",L,j,$,V,q,J,W,K=e[21].MemberType+"",re,se,Q,G,B,Y,ae=e[21].CardType+"",de,Pe,ke,pe,Ne,De,ee=yl(e[21].PlanEffFrom)+"",le,bt,Ve,Ae,rt,st,je=yl(e[21].PlanEffTo)+"",We,ht,Ue,Re,at,ot,Fe=(e[21].InsurerName||e[21].InsurerNameEN)+"",Qe,_t,Oe,xe,$e=e[21].PlanName+"",yt,ct,ut,Xe,vt,qe,oe=e[21].VIP==="Y"&&yr(),ce=e[21].CompanyName&&vr(e);function fe(){return e[17](e[21])}return{key:t,first:null,c(){l=f("button"),n=f("div"),r=f("div"),o=f("h2"),s=f("span"),u=v(c),d=p(),g=v(m),_=p(),y=f("div"),S=f("span"),w=v(h),z=p(),oe&&oe.c(),I=p(),E=f("p"),P=v("กรมธรรม์ #"),k=v(C),O=p(),D=f("p"),M=v("ใบรับรอง #"),L=v(H),j=p(),$=f("div"),V=f("div"),q=f("span"),q.textContent="ประเภทสมาชิก:",J=p(),W=f("span"),re=v(K),se=p(),Q=f("div"),G=f("span"),G.textContent="ประเภทบัตร:",B=p(),Y=f("span"),de=v(ae),Pe=p(),ke=f("div"),pe=f("span"),pe.textContent="วันที่เริ่มต้น:",Ne=p(),De=f("span"),le=v(ee),bt=p(),Ve=f("div"),Ae=f("span"),Ae.textContent="วันที่สิ้นสุด:",rt=p(),st=f("span"),We=v(je),ht=p(),Ue=f("div"),Re=f("span"),Re.textContent="บริษัทประกัน:",at=p(),ot=f("span"),Qe=v(Fe),_t=p(),Oe=f("div"),xe=f("p"),yt=v($e),ct=p(),ce&&ce.c(),ut=p(),a(s,"class","text-2xl"),a(s,"aria-hidden","true"),a(o,"id",b="policy-"+e[21].MemberCode+"-title"),a(o,"class","text-lg font-semibold text-gray-900 flex items-center gap-2"),a(S,"class",A="px-3 py-1 rounded-full text-xs font-medium border "+(e[8][e[21].MemberStatus]||e[8].Active)),a(S,"aria-label",R="Member status: "+e[21].MemberStatus),a(y,"class","flex flex-col items-end gap-1"),a(r,"class","flex items-center justify-between mb-3"),a(E,"class","text-sm text-gray-600 mb-2"),a(D,"class","text-sm text-gray-600 mb-2"),a(n,"class","mb-4"),a(q,"class","text-sm text-gray-500"),a(W,"class","text-sm font-medium text-gray-900"),a(V,"class","flex justify-between items-center"),a(G,"class","text-sm text-gray-500"),a(Y,"class","text-sm font-medium text-gray-900"),a(Q,"class","flex justify-between items-center"),a(pe,"class","text-sm text-gray-500"),a(De,"class","text-sm font-medium text-gray-900"),a(ke,"class","flex justify-between items-center"),a(Ae,"class","text-sm text-gray-500"),a(st,"class","text-sm font-medium text-gray-900"),a(Ve,"class","flex justify-between items-center"),a(Re,"class","text-sm text-gray-500"),a(ot,"class","text-sm font-medium text-gray-900 text-right"),a(Ue,"class","flex justify-between items-center"),a($,"class","space-y-3 mb-4 flex-grow"),a(xe,"class","text-xs text-gray-600 leading-relaxed line-clamp-2"),a(Oe,"class","border-t border-gray-100 pt-4"),a(l,"class","bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out hover:scale-102 transform border border-gray-100 min-h-[320px] p-6 flex flex-col justify-between cursor-pointer text-left w-full"),a(l,"aria-labelledby",Xe="policy-"+e[21].MemberCode+"-title"),this.first=l},m(Me,te){x(Me,l,te),i(l,n),i(n,r),i(r,o),i(o,s),i(s,u),i(o,d),i(o,g),i(r,_),i(r,y),i(y,S),i(S,w),i(y,z),oe&&oe.m(y,null),i(n,I),i(n,E),i(E,P),i(E,k),i(n,O),i(n,D),i(D,M),i(D,L),i(l,j),i(l,$),i($,V),i(V,q),i(V,J),i(V,W),i(W,re),i($,se),i($,Q),i(Q,G),i(Q,B),i(Q,Y),i(Y,de),i($,Pe),i($,ke),i(ke,pe),i(ke,Ne),i(ke,De),i(De,le),i($,bt),i($,Ve),i(Ve,Ae),i(Ve,rt),i(Ve,st),i(st,We),i($,ht),i($,Ue),i(Ue,Re),i(Ue,at),i(Ue,ot),i(ot,Qe),i(l,_t),i(l,Oe),i(Oe,xe),i(xe,yt),i(Oe,ct),ce&&ce.m(Oe,null),i(l,ut),vt||(qe=ie(l,"click",fe),vt=!0)},p(Me,te){e=Me,te&128&&c!==(c=e[9](e[21].PlanName)+"")&&T(u,c),te&128&&m!==(m=en(e[21].PlanName)+"")&&T(g,m),te&128&&b!==(b="policy-"+e[21].MemberCode+"-title")&&a(o,"id",b),te&128&&h!==(h=e[21].MemberStatus+"")&&T(w,h),te&128&&A!==(A="px-3 py-1 rounded-full text-xs font-medium border "+(e[8][e[21].MemberStatus]||e[8].Active))&&a(S,"class",A),te&128&&R!==(R="Member status: "+e[21].MemberStatus)&&a(S,"aria-label",R),e[21].VIP==="Y"?oe||(oe=yr(),oe.c(),oe.m(y,null)):oe&&(oe.d(1),oe=null),te&128&&C!==(C=e[21].PolicyNo+"")&&T(k,C),te&128&&H!==(H=e[21].CertificateNo+"")&&T(L,H),te&128&&K!==(K=e[21].MemberType+"")&&T(re,K),te&128&&ae!==(ae=e[21].CardType+"")&&T(de,ae),te&128&&ee!==(ee=yl(e[21].PlanEffFrom)+"")&&T(le,ee),te&128&&je!==(je=yl(e[21].PlanEffTo)+"")&&T(We,je),te&128&&Fe!==(Fe=(e[21].InsurerName||e[21].InsurerNameEN)+"")&&T(Qe,Fe),te&128&&$e!==($e=e[21].PlanName+"")&&T(yt,$e),e[21].CompanyName?ce?ce.p(e,te):(ce=vr(e),ce.c(),ce.m(Oe,null)):ce&&(ce.d(1),ce=null),te&128&&Xe!==(Xe="policy-"+e[21].MemberCode+"-title")&&a(l,"aria-labelledby",Xe)},d(Me){Me&&N(l),oe&&oe.d(),ce&&ce.d(),vt=!1,qe()}}}function lo(t){let e,l,n,r,o,s,c,u,d,m,g,b,_,y;d=new Ga({props:{compact:!1,showLabels:!0,horizontal:!0}});function S(I,E){return I[0]?Za:I[2]&&I[1]?Ka:Ja}let h=S(t),w=h(t);const A=[to,eo,Xa,Qa,Wa],R=[];function z(I,E){return I[3]?I[6]?1:I[5]?2:I[7].length===0?3:4:0}return b=z(t),_=R[b]=A[b](t),{c(){e=f("main"),l=f("div"),n=f("header"),r=f("h1"),r.textContent="กรมธรรม์ประกันภัย",o=p(),s=f("div"),c=f("h2"),c.textContent="เลือกข้อมูลสมาชิก",u=p(),Ke(d.$$.fragment),m=p(),w.c(),g=p(),_.c(),a(r,"class","text-3xl font-bold text-gray-900 mb-6"),a(c,"class","text-lg font-semibold text-gray-900 mb-4"),a(s,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"),a(n,"class","mb-8"),a(l,"class","max-w-7xl mx-auto"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8"),a(e,"aria-label","Insurance Policy List")},m(I,E){x(I,e,E),i(e,l),i(l,n),i(n,r),i(n,o),i(n,s),i(s,c),i(s,u),Le(d,s,null),i(n,m),w.m(n,null),i(l,g),R[b].m(l,null),y=!0},p(I,[E]){h===(h=S(I))&&w?w.p(I,E):(w.d(1),w=h(I),w&&(w.c(),w.m(n,null)));let P=b;b=z(I),b===P?R[b].p(I,E):(rn(),be(R[P],1,1,()=>{R[P]=null}),sn(),_=R[b],_?_.p(I,E):(_=R[b]=A[b](I),_.c()),ue(_,1),_.m(l,null))},i(I){y||(ue(d.$$.fragment,I),ue(_),y=!0)},o(I){be(d.$$.fragment,I),be(_),y=!1},d(I){I&&N(e),ze(d),w.d(),R[b].d()}}}function yl(t){return t?new Date(t).toLocaleDateString("th-TH",{day:"2-digit",month:"2-digit",year:"numeric"}):"ไม่ระบุ"}function en(t){return t?t.includes("สุขภาพ")?"Health":t.includes("ชีวิต")?"Life":t.includes("Executive")?"Executive":t.includes("Premium")?"Premium":t.includes("Basic")||t.includes("พื้นฐาน")?"Basic":"General":"General"}function no(t,e,l){let n,r,o,s,c,u,d,m,g,b,_,y,S;Te(t,lt,C=>l(12,g=C)),Te(t,Dt,C=>l(13,b=C)),Te(t,Pt,C=>l(14,_=C)),Te(t,dn,C=>l(15,y=C)),Te(t,me,C=>l(16,S=C));const h=nn(),w={Auto:"🚗",Home:"🏠",Life:"❤️",Health:"🏥",Medical:"🏥",Business:"🏢",General:"📄",Basic:"📋",Premium:"⭐",Executive:"💼"},A={Active:"bg-green-100 text-green-800 border-green-200",Inactive:"bg-gray-100 text-gray-800 border-gray-200",Expired:"bg-red-100 text-red-800 border-red-200",Pending:"bg-yellow-100 text-yellow-800 border-yellow-200",Cancelled:"bg-gray-100 text-gray-800 border-gray-200"};function R(C){const k=en(C);return w[k]||w.General}function z(C){h("navigate",{page:"policy-detail",memberCode:C})}async function I(){var C;if(!s){console.warn("No member selected, cannot load policies"),lt.setError(new Error("กรุณาเลือกสมาชิกก่อนดูข้อมูลกรมธรรม์"));return}if(!s.insurerCode||!s.citizenID){console.warn("Selected member missing required data for API call"),lt.setError(new Error("ข้อมูลสมาชิกไม่ครบถ้วน"));return}try{const k={insurerCode:s.insurerCode,citizenId:s.citizenID};await ya(k),console.log(`Policies loaded successfully for member ${s.memberCode}:`,((C=g.data)==null?void 0:C.length)||0)}catch(k){console.error("Failed to load policies:",k)}}async function E(){await I()}Cl(()=>{console.log("PolicyList component mounted")});const P=C=>z(C.MemberCode);return t.$$.update=()=>{t.$$.dirty&4096&&l(7,n=g.data||[]),t.$$.dirty&4096&&l(6,r=g.loading),t.$$.dirty&4096&&l(5,o=g.error),t.$$.dirty&65536&&l(0,s=S),t.$$.dirty&32768&&l(4,c=y),t.$$.dirty&16384&&l(2,u=_),t.$$.dirty&8192&&l(1,d=b),t.$$.dirty&6&&l(3,m=u&&d),t.$$.dirty&1&&s&&I()},[s,d,u,m,c,o,r,n,A,R,z,E,g,b,_,y,S,P]}class ds extends Mt{constructor(e){super(),xt(this,e,no,lo,pt,{})}}function Er(t,e,l){const n=t.slice();return n[23]=e[l],n}function Cr(t,e,l){const n=t.slice();return n[23]=e[l],n}function Tr(t,e,l){const n=t.slice();return n[28]=e[l],n}function Ir(t,e,l){const n=t.slice();return n[31]=e[l],n}function kr(t,e,l){const n=t.slice();return n[31]=e[l],n}function io(t){var zn,Bn,Vn,jn,Un,Fn,$n,qn,Yn,Gn,Jn,Kn,Zn,Wn,Qn,Xn,ei,ti,li,ni,ii,ri,si,ai,oi;let e,l,n,r,o,s,c,u,d,m,g,b,_,y,S,h,w,A,R,z,I,E,P,C,k,O,D,M,H,L,j=(((zn=t[1])==null?void 0:zn.PolicyNo)||"ไม่ระบุ")+"",$,V,q,J,W,K,re=(((Bn=t[1])==null?void 0:Bn.CertificateNo)||"ไม่ระบุ")+"",se,Q,G,B,Y,ae,de=(((Vn=t[1])==null?void 0:Vn.PlanName)||"ไม่ระบุ")+"",Pe,ke,pe,Ne,De,ee,le=Ce((jn=t[1])==null?void 0:jn.PlanEffFrom)+"",bt,Ve,Ae,rt,st,je,We=Ce((Un=t[1])==null?void 0:Un.PlanEffTo)+"",ht,Ue,Re,at,ot,Fe,Qe=(((Fn=t[1])==null?void 0:Fn.InsurerName)||(($n=t[1])==null?void 0:$n.InsurerNameEN)||"ไม่ระบุ")+"",_t,Oe,xe,$e,yt,ct,ut=(((qn=t[1])==null?void 0:qn.CompanyName)||((Yn=t[1])==null?void 0:Yn.CompanyNameEN)||"ไม่ระบุ")+"",Xe,vt,qe,oe,ce,fe,Me,te,mn,et,$t=(((Gn=t[0])==null?void 0:Gn.titleTH)||"")+"",Ml,pn,qt=(((Jn=t[0])==null?void 0:Jn.nameTH)||"")+"",Sl,gn,Yt=(((Kn=t[0])==null?void 0:Kn.surnameTH)||"")+"",Pl,bn,At,Gt,hn,tt,Jt=(((Zn=t[0])==null?void 0:Zn.titleEN)||"")+"",Dl,_n,Kt=(((Wn=t[0])==null?void 0:Wn.nameEN)||"")+"",Al,yn,Zt=(((Qn=t[0])==null?void 0:Qn.surnameEN)||"")+"",Rl,vn,Rt,Wt,wn,Qt,Xt=(((Xn=t[0])==null?void 0:Xn.citizenID)||"ไม่ระบุ")+"",Ol,En,Ot,el,Cn,tl,ll=Ce((ei=t[0])==null?void 0:ei.birthDate)+"",Hl,Tn,Ht,nl,In,il,rl=((ti=t[0])==null?void 0:ti.gender)==="M"?"ชาย":((li=t[0])==null?void 0:li.gender)==="F"?"หญิง":"ไม่ระบุ",Ll,kn,Lt,sl,Nn,al,ol=(((ni=t[0])==null?void 0:ni.memberType)==="Principal"?"ผู้เอาประกันหลัก":((ii=t[0])==null?void 0:ii.memberType)==="Dependent"?"ผู้อยู่ในอุปการะ":((ri=t[0])==null?void 0:ri.memberType)||"ไม่ระบุ")+"",zl,xn,Bl,Vl,dt,cl,Mn,Sn,jl,Pn,ul,wt,dl,Dn,Et,zt,fl,An,ml,pl=(((si=t[0])==null?void 0:si.mobile)||"ไม่ระบุ")+"",Ul,Rn,Bt,gl,On,bl,hl=(((ai=t[0])==null?void 0:ai.email)||"ไม่ระบุ")+"",Fl,$l,Hn,he=((oi=t[0])==null?void 0:oi.memberCode)&&Nr(t),_e=t[0]&&xr(t),ye=t[6].length>0&&Pr(t),ve=t[5].length>0&&Ar(t);function Ln(U,Z){return U[2].length>0?uo:co}let _l=Ln(t),He=_l(t),we=t[4].length>0&&Hr(t),Ee=t[3].length>0&&Br(t);return{c(){e=f("main"),l=f("div"),n=f("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,r=p(),o=f("div"),s=f("div"),c=f("div"),u=f("div"),d=f("span"),d.textContent="📄",m=p(),g=f("div"),b=f("h1"),b.textContent="รายละเอียดกรมธรรม์ประกันภัย",_=p(),y=f("p"),S=v("สมาชิก: "),h=f("span"),w=v(t[8]),A=p(),he&&he.c(),R=p(),_e&&_e.c(),z=p(),I=f("div"),E=f("div"),P=f("section"),C=f("h2"),C.textContent="ข้อมูลกรมธรรม์",k=p(),O=f("div"),D=f("div"),M=f("div"),M.textContent="หมายเลขกรมธรรม์",H=p(),L=f("p"),$=v(j),V=p(),q=f("div"),J=f("div"),J.textContent="หมายเลขใบรับรอง",W=p(),K=f("p"),se=v(re),Q=p(),G=f("div"),B=f("div"),B.textContent="แผนประกันภัย",Y=p(),ae=f("p"),Pe=v(de),ke=p(),pe=f("div"),Ne=f("div"),Ne.textContent="วันที่มีผลบังคับใช้",De=p(),ee=f("p"),bt=v(le),Ve=p(),Ae=f("div"),rt=f("div"),rt.textContent="วันที่สิ้นสุด",st=p(),je=f("p"),ht=v(We),Ue=p(),Re=f("div"),at=f("div"),at.textContent="บริษัทประกันภัย",ot=p(),Fe=f("p"),_t=v(Qe),Oe=p(),xe=f("div"),$e=f("div"),$e.textContent="บริษัทที่ทำงาน",yt=p(),ct=f("p"),Xe=v(ut),vt=p(),qe=f("section"),oe=f("h2"),oe.textContent="ข้อมูลสมาชิก",ce=p(),fe=f("div"),Me=f("div"),te=f("div"),te.textContent="ชื่อ-นามสกุล (ไทย)",mn=p(),et=f("p"),Ml=v($t),pn=p(),Sl=v(qt),gn=p(),Pl=v(Yt),bn=p(),At=f("div"),Gt=f("div"),Gt.textContent="ชื่อ-นามสกุล (อังกฤษ)",hn=p(),tt=f("p"),Dl=v(Jt),_n=p(),Al=v(Kt),yn=p(),Rl=v(Zt),vn=p(),Rt=f("div"),Wt=f("div"),Wt.textContent="เลขบัตรประชาชน",wn=p(),Qt=f("p"),Ol=v(Xt),En=p(),Ot=f("div"),el=f("div"),el.textContent="วันเกิด",Cn=p(),tl=f("p"),Hl=v(ll),Tn=p(),Ht=f("div"),nl=f("div"),nl.textContent="เพศ",In=p(),il=f("p"),Ll=v(rl),kn=p(),Lt=f("div"),sl=f("div"),sl.textContent="ประเภทสมาชิก",Nn=p(),al=f("p"),zl=v(ol),xn=p(),ye&&ye.c(),Bl=p(),ve&&ve.c(),Vl=p(),dt=f("section"),cl=f("h2"),cl.textContent="ประวัติการเคลม",Mn=p(),He.c(),Sn=p(),we&&we.c(),jl=p(),Ee&&Ee.c(),Pn=p(),ul=f("div"),wt=f("section"),dl=f("h2"),dl.textContent="ข้อมูลติดต่อ",Dn=p(),Et=f("div"),zt=f("div"),fl=f("div"),fl.textContent="เบอร์โทรศัพท์",An=p(),ml=f("p"),Ul=v(pl),Rn=p(),Bt=f("div"),gl=f("div"),gl.textContent="อีเมล",On=p(),bl=f("p"),Fl=v(hl),a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-6 sm:mb-8"),a(d,"class","text-4xl mr-4"),a(d,"aria-hidden","true"),a(b,"class","text-2xl sm:text-3xl font-bold text-gray-900 mb-1"),a(h,"class","font-medium"),a(y,"class","text-gray-600"),a(u,"class","flex items-center mb-4 lg:mb-0"),a(c,"class","flex flex-col lg:flex-row lg:items-center lg:justify-between"),a(s,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"),a(C,"class","text-xl font-semibold text-gray-900 mb-4"),a(M,"class","text-sm font-medium text-gray-500 mb-1"),a(L,"class","text-lg font-semibold text-gray-900"),a(J,"class","text-sm font-medium text-gray-500 mb-1"),a(K,"class","text-lg font-semibold text-gray-900"),a(B,"class","text-sm font-medium text-gray-500 mb-1"),a(ae,"class","text-gray-900"),a(Ne,"class","text-sm font-medium text-gray-500 mb-1"),a(ee,"class","text-gray-900"),a(rt,"class","text-sm font-medium text-gray-500 mb-1"),a(je,"class","text-gray-900"),a(at,"class","text-sm font-medium text-gray-500 mb-1"),a(Fe,"class","text-gray-900"),a(O,"class","grid gap-4 sm:grid-cols-2"),a($e,"class","text-sm font-medium text-gray-500 mb-2"),a(ct,"class","text-gray-700 leading-relaxed"),a(xe,"class","mt-4 pt-4 border-t border-gray-200"),a(P,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6"),a(oe,"class","text-xl font-semibold text-gray-900 mb-4"),a(te,"class","text-sm font-medium text-gray-500 mb-1"),a(et,"class","text-gray-900"),a(Gt,"class","text-sm font-medium text-gray-500 mb-1"),a(tt,"class","text-gray-900"),a(Wt,"class","text-sm font-medium text-gray-500 mb-1"),a(Qt,"class","text-gray-900 font-mono"),a(el,"class","text-sm font-medium text-gray-500 mb-1"),a(tl,"class","text-gray-900"),a(nl,"class","text-sm font-medium text-gray-500 mb-1"),a(il,"class","text-gray-900"),a(sl,"class","text-sm font-medium text-gray-500 mb-1"),a(al,"class","text-gray-900"),a(fe,"class","grid gap-4 sm:grid-cols-2"),a(qe,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6"),a(cl,"class","text-xl font-semibold text-gray-900 mb-4"),a(dt,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6"),a(E,"class","lg:col-span-2 xl:col-span-3 space-y-6"),a(dl,"class","text-lg font-semibold text-gray-900 mb-4"),a(fl,"class","text-sm font-medium text-gray-500 mb-1"),a(ml,"class","text-gray-900"),a(gl,"class","text-sm font-medium text-gray-500 mb-1"),a(bl,"class","text-gray-900 break-all"),a(Et,"class","space-y-3"),a(wt,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6"),a(ul,"class","lg:col-span-1 xl:col-span-1 space-y-6"),a(I,"class","grid gap-6 grid-cols-1 lg:grid-cols-3 xl:grid-cols-4"),a(o,"class","max-w-7xl mx-auto"),a(e,"class","min-h-screen bg-gray-50 py-4 px-4 sm:py-8 sm:px-6 lg:px-8")},m(U,Z){x(U,e,Z),i(e,l),i(l,n),i(e,r),i(e,o),i(o,s),i(s,c),i(c,u),i(u,d),i(u,m),i(u,g),i(g,b),i(g,_),i(g,y),i(y,S),i(y,h),i(h,w),i(y,A),he&&he.m(y,null),i(c,R),_e&&_e.m(c,null),i(o,z),i(o,I),i(I,E),i(E,P),i(P,C),i(P,k),i(P,O),i(O,D),i(D,M),i(D,H),i(D,L),i(L,$),i(O,V),i(O,q),i(q,J),i(q,W),i(q,K),i(K,se),i(O,Q),i(O,G),i(G,B),i(G,Y),i(G,ae),i(ae,Pe),i(O,ke),i(O,pe),i(pe,Ne),i(pe,De),i(pe,ee),i(ee,bt),i(O,Ve),i(O,Ae),i(Ae,rt),i(Ae,st),i(Ae,je),i(je,ht),i(O,Ue),i(O,Re),i(Re,at),i(Re,ot),i(Re,Fe),i(Fe,_t),i(P,Oe),i(P,xe),i(xe,$e),i(xe,yt),i(xe,ct),i(ct,Xe),i(E,vt),i(E,qe),i(qe,oe),i(qe,ce),i(qe,fe),i(fe,Me),i(Me,te),i(Me,mn),i(Me,et),i(et,Ml),i(et,pn),i(et,Sl),i(et,gn),i(et,Pl),i(fe,bn),i(fe,At),i(At,Gt),i(At,hn),i(At,tt),i(tt,Dl),i(tt,_n),i(tt,Al),i(tt,yn),i(tt,Rl),i(fe,vn),i(fe,Rt),i(Rt,Wt),i(Rt,wn),i(Rt,Qt),i(Qt,Ol),i(fe,En),i(fe,Ot),i(Ot,el),i(Ot,Cn),i(Ot,tl),i(tl,Hl),i(fe,Tn),i(fe,Ht),i(Ht,nl),i(Ht,In),i(Ht,il),i(il,Ll),i(fe,kn),i(fe,Lt),i(Lt,sl),i(Lt,Nn),i(Lt,al),i(al,zl),i(E,xn),ye&&ye.m(E,null),i(E,Bl),ve&&ve.m(E,null),i(E,Vl),i(E,dt),i(dt,cl),i(dt,Mn),He.m(dt,null),i(E,Sn),we&&we.m(E,null),i(E,jl),Ee&&Ee.m(E,null),i(I,Pn),i(I,ul),i(ul,wt),i(wt,dl),i(wt,Dn),i(wt,Et),i(Et,zt),i(zt,fl),i(zt,An),i(zt,ml),i(ml,Ul),i(Et,Rn),i(Et,Bt),i(Bt,gl),i(Bt,On),i(Bt,bl),i(bl,Fl),$l||(Hn=ie(n,"click",t[11]),$l=!0)},p(U,Z){var ci,ui,di,fi,mi,pi,gi,bi,hi,_i,yi,vi,wi,Ei,Ci,Ti,Ii,ki,Ni,xi,Mi,Si,Pi,Di,Ai;Z[0]&256&&T(w,U[8]),(ci=U[0])!=null&&ci.memberCode?he?he.p(U,Z):(he=Nr(U),he.c(),he.m(y,null)):he&&(he.d(1),he=null),U[0]?_e?_e.p(U,Z):(_e=xr(U),_e.c(),_e.m(c,null)):_e&&(_e.d(1),_e=null),Z[0]&2&&j!==(j=(((ui=U[1])==null?void 0:ui.PolicyNo)||"ไม่ระบุ")+"")&&T($,j),Z[0]&2&&re!==(re=(((di=U[1])==null?void 0:di.CertificateNo)||"ไม่ระบุ")+"")&&T(se,re),Z[0]&2&&de!==(de=(((fi=U[1])==null?void 0:fi.PlanName)||"ไม่ระบุ")+"")&&T(Pe,de),Z[0]&2&&le!==(le=Ce((mi=U[1])==null?void 0:mi.PlanEffFrom)+"")&&T(bt,le),Z[0]&2&&We!==(We=Ce((pi=U[1])==null?void 0:pi.PlanEffTo)+"")&&T(ht,We),Z[0]&2&&Qe!==(Qe=(((gi=U[1])==null?void 0:gi.InsurerName)||((bi=U[1])==null?void 0:bi.InsurerNameEN)||"ไม่ระบุ")+"")&&T(_t,Qe),Z[0]&2&&ut!==(ut=(((hi=U[1])==null?void 0:hi.CompanyName)||((_i=U[1])==null?void 0:_i.CompanyNameEN)||"ไม่ระบุ")+"")&&T(Xe,ut),Z[0]&1&&$t!==($t=(((yi=U[0])==null?void 0:yi.titleTH)||"")+"")&&T(Ml,$t),Z[0]&1&&qt!==(qt=(((vi=U[0])==null?void 0:vi.nameTH)||"")+"")&&T(Sl,qt),Z[0]&1&&Yt!==(Yt=(((wi=U[0])==null?void 0:wi.surnameTH)||"")+"")&&T(Pl,Yt),Z[0]&1&&Jt!==(Jt=(((Ei=U[0])==null?void 0:Ei.titleEN)||"")+"")&&T(Dl,Jt),Z[0]&1&&Kt!==(Kt=(((Ci=U[0])==null?void 0:Ci.nameEN)||"")+"")&&T(Al,Kt),Z[0]&1&&Zt!==(Zt=(((Ti=U[0])==null?void 0:Ti.surnameEN)||"")+"")&&T(Rl,Zt),Z[0]&1&&Xt!==(Xt=(((Ii=U[0])==null?void 0:Ii.citizenID)||"ไม่ระบุ")+"")&&T(Ol,Xt),Z[0]&1&&ll!==(ll=Ce((ki=U[0])==null?void 0:ki.birthDate)+"")&&T(Hl,ll),Z[0]&1&&rl!==(rl=((Ni=U[0])==null?void 0:Ni.gender)==="M"?"ชาย":((xi=U[0])==null?void 0:xi.gender)==="F"?"หญิง":"ไม่ระบุ")&&T(Ll,rl),Z[0]&1&&ol!==(ol=(((Mi=U[0])==null?void 0:Mi.memberType)==="Principal"?"ผู้เอาประกันหลัก":((Si=U[0])==null?void 0:Si.memberType)==="Dependent"?"ผู้อยู่ในอุปการะ":((Pi=U[0])==null?void 0:Pi.memberType)||"ไม่ระบุ")+"")&&T(zl,ol),U[6].length>0?ye?ye.p(U,Z):(ye=Pr(U),ye.c(),ye.m(E,Bl)):ye&&(ye.d(1),ye=null),U[5].length>0?ve?ve.p(U,Z):(ve=Ar(U),ve.c(),ve.m(E,Vl)):ve&&(ve.d(1),ve=null),_l===(_l=Ln(U))&&He?He.p(U,Z):(He.d(1),He=_l(U),He&&(He.c(),He.m(dt,null))),U[4].length>0?we?we.p(U,Z):(we=Hr(U),we.c(),we.m(E,jl)):we&&(we.d(1),we=null),U[3].length>0?Ee?Ee.p(U,Z):(Ee=Br(U),Ee.c(),Ee.m(E,null)):Ee&&(Ee.d(1),Ee=null),Z[0]&1&&pl!==(pl=(((Di=U[0])==null?void 0:Di.mobile)||"ไม่ระบุ")+"")&&T(Ul,pl),Z[0]&1&&hl!==(hl=(((Ai=U[0])==null?void 0:Ai.email)||"ไม่ระบุ")+"")&&T(Fl,hl)},i:F,o:F,d(U){U&&N(e),he&&he.d(),_e&&_e.d(),ye&&ye.d(),ve&&ve.d(),He.d(),we&&we.d(),Ee&&Ee.d(),$l=!1,Hn()}}}function ro(t){let e,l,n,r,o,s,c,u;return o=new fn({props:{variant:"not-found",message:"ไม่พบข้อมูลกรมธรรม์ที่ร้องขอ"}}),o.$on("goBack",t[11]),{c(){e=f("main"),l=f("div"),n=f("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,r=p(),Ke(o.$$.fragment),a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-8"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8")},m(d,m){x(d,e,m),i(e,l),i(l,n),i(e,r),Le(o,e,null),s=!0,c||(u=ie(n,"click",t[11]),c=!0)},p:F,i(d){s||(ue(o.$$.fragment,d),s=!0)},o(d){be(o.$$.fragment,d),s=!1},d(d){d&&N(e),ze(o),c=!1,u()}}}function so(t){let e,l,n,r,o,s,c,u;return o=new fn({props:{variant:"api",error:t[9],message:"ไม่สามารถโหลดรายละเอียดกรมธรรม์ได้ กรุณาลองใหม่อีกครั้ง"}}),o.$on("retry",t[13]),o.$on("goBack",t[11]),{c(){e=f("main"),l=f("div"),n=f("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,r=p(),Ke(o.$$.fragment),a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-8"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8")},m(d,m){x(d,e,m),i(e,l),i(l,n),i(e,r),Le(o,e,null),s=!0,c||(u=ie(n,"click",t[11]),c=!0)},p(d,m){const g={};m[0]&512&&(g.error=d[9]),o.$set(g)},i(d){s||(ue(o.$$.fragment,d),s=!0)},o(d){be(o.$$.fragment,d),s=!1},d(d){d&&N(e),ze(o),c=!1,u()}}}function ao(t){let e,l,n,r,o,s,c,u;return o=new us({props:{variant:"detail",message:"กำลังโหลดรายละเอียดกรมธรรม์..."}}),{c(){e=f("main"),l=f("div"),n=f("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,r=p(),Ke(o.$$.fragment),a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-8"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8")},m(d,m){x(d,e,m),i(e,l),i(l,n),i(e,r),Le(o,e,null),s=!0,c||(u=ie(n,"click",t[11]),c=!0)},p:F,i(d){s||(ue(o.$$.fragment,d),s=!0)},o(d){be(o.$$.fragment,d),s=!1},d(d){d&&N(e),ze(o),c=!1,u()}}}function oo(t){let e,l,n,r,o,s,c,u,d,m,g,b,_,y;return{c(){e=f("main"),l=f("div"),n=f("button"),n.innerHTML=`<svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
        กลับไปรายการกรมธรรม์`,r=p(),o=f("div"),s=f("div"),s.textContent="�",c=p(),u=f("h1"),u.textContent="ไม่ได้เลือกสมาชิก",d=p(),m=f("p"),m.textContent="กรุณาเลือกสมาชิกจากเมนูด้านบนเพื่อดูรายละเอียดกรมธรรม์",g=p(),b=f("button"),b.textContent="ดูรายการกรมธรรม์",a(n,"class","inline-flex items-center px-4 py-2 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-md border border-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(n,"aria-label","กลับไปยังรายการกรมธรรม์"),a(l,"class","max-w-7xl mx-auto mb-8"),a(s,"class","text-8xl mb-6"),a(s,"aria-hidden","true"),a(u,"class","text-4xl font-bold text-gray-900 mb-4"),a(m,"class","text-xl text-gray-600 max-w-2xl mx-auto mb-8"),a(b,"class","inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),a(b,"aria-label","ไปยังรายการกรมธรรม์"),a(o,"class","max-w-4xl mx-auto text-center"),a(e,"class","min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8")},m(S,h){x(S,e,h),i(e,l),i(l,n),i(e,r),i(e,o),i(o,s),i(o,c),i(o,u),i(o,d),i(o,m),i(o,g),i(o,b),_||(y=[ie(n,"click",t[11]),ie(b,"click",t[11])],_=!0)},p:F,i:F,o:F,d(S){S&&N(e),_=!1,Je(y)}}}function Nr(t){let e,l,n=t[0].memberCode+"",r,o;return{c(){e=f("span"),l=v("("),r=v(n),o=v(")"),a(e,"class","text-sm text-gray-500 ml-2")},m(s,c){x(s,e,c),i(e,l),i(e,r),i(e,o)},p(s,c){c[0]&1&&n!==(n=s[0].memberCode+"")&&T(r,n)},d(s){s&&N(e)}}}function xr(t){let e,l,n=t[0].memberStatus+"",r,o,s,c,u,d=t[0].vip==="Y"&&Mr(),m=t[0].cardType&&t[0].cardType!=="Standard"&&Sr(t);return{c(){e=f("div"),l=f("span"),r=v(n),c=p(),d&&d.c(),u=p(),m&&m.c(),a(l,"class",o="px-4 py-2 rounded-full text-sm font-medium border text-center "+(t[12][t[0].memberStatus]||t[12].Active)),a(l,"aria-label",s="สถานะสมาชิก: "+t[0].memberStatus),a(e,"class","flex flex-col sm:flex-row sm:items-center gap-3")},m(g,b){x(g,e,b),i(e,l),i(l,r),i(e,c),d&&d.m(e,null),i(e,u),m&&m.m(e,null)},p(g,b){b[0]&1&&n!==(n=g[0].memberStatus+"")&&T(r,n),b[0]&1&&o!==(o="px-4 py-2 rounded-full text-sm font-medium border text-center "+(g[12][g[0].memberStatus]||g[12].Active))&&a(l,"class",o),b[0]&1&&s!==(s="สถานะสมาชิก: "+g[0].memberStatus)&&a(l,"aria-label",s),g[0].vip==="Y"?d||(d=Mr(),d.c(),d.m(e,u)):d&&(d.d(1),d=null),g[0].cardType&&g[0].cardType!=="Standard"?m?m.p(g,b):(m=Sr(g),m.c(),m.m(e,null)):m&&(m.d(1),m=null)},d(g){g&&N(e),d&&d.d(),m&&m.d()}}}function Mr(t){let e;return{c(){e=f("span"),e.textContent="VIP",a(e,"class","px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200")},m(l,n){x(l,e,n)},d(l){l&&N(e)}}}function Sr(t){let e,l=t[0].cardType+"",n,r;return{c(){e=f("span"),n=v(l),a(e,"class",r="px-3 py-1 rounded-full text-xs font-medium "+(t[0].cardType==="Gold"?"bg-yellow-100 text-yellow-800 border border-yellow-200":t[0].cardType==="Platinum"?"bg-gray-100 text-gray-800 border border-gray-200":t[0].cardType==="Diamond"?"bg-blue-100 text-blue-800 border border-blue-200":"bg-gray-100 text-gray-800 border border-gray-200"))},m(o,s){x(o,e,s),i(e,n)},p(o,s){s[0]&1&&l!==(l=o[0].cardType+"")&&T(n,l),s[0]&1&&r!==(r="px-3 py-1 rounded-full text-xs font-medium "+(o[0].cardType==="Gold"?"bg-yellow-100 text-yellow-800 border border-yellow-200":o[0].cardType==="Platinum"?"bg-gray-100 text-gray-800 border border-gray-200":o[0].cardType==="Diamond"?"bg-blue-100 text-blue-800 border border-blue-200":"bg-gray-100 text-gray-800 border border-gray-200"))&&a(e,"class",r)},d(o){o&&N(e)}}}function Pr(t){let e,l,n,r,o=X(t[6]),s=[];for(let c=0;c<o.length;c+=1)s[c]=Dr(kr(t,o,c));return{c(){e=f("section"),l=f("h2"),l.textContent="ความคุ้มครองหลัก",n=p(),r=f("div");for(let c=0;c<s.length;c+=1)s[c].c();a(l,"class","text-xl font-semibold text-gray-900 mb-4"),a(r,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6")},m(c,u){x(c,e,u),i(e,l),i(e,n),i(e,r);for(let d=0;d<s.length;d+=1)s[d]&&s[d].m(r,null)},p(c,u){if(u[0]&64){o=X(c[6]);let d;for(d=0;d<o.length;d+=1){const m=kr(c,o,d);s[d]?s[d].p(m,u):(s[d]=Dr(m),s[d].c(),s[d].m(r,null))}for(;d<s.length;d+=1)s[d].d(1);s.length=o.length}},d(c){c&&N(e),Ie(s,c)}}}function Dr(t){let e,l,n,r,o=t[31].MainBenefit+"",s,c,u,d=t[31].MainBenefitEN+"",m,g,b,_=t[31].MainPlanLimitDesc+"",y,S,h,w,A=nt(parseInt(t[31].MainPlanAmount)||0)+"",R,z,I,E=t[31].MainPlanUnit1+"",P,C,k=t[31].MainPlanUnit2+"",O,D,M,H,L=nt(parseInt(t[31].MainPlanBalance)||0)+"",j,$;return{c(){e=f("div"),l=f("div"),n=f("div"),r=f("h3"),s=v(o),c=p(),u=f("p"),m=v(d),g=p(),b=f("p"),y=v(_),S=p(),h=f("div"),w=f("div"),R=v(A),z=p(),I=f("div"),P=v(E),C=v("/"),O=v(k),D=p(),M=f("div"),H=v("คงเหลือ: "),j=v(L),$=p(),a(r,"class","font-medium text-gray-900"),a(u,"class","text-sm text-gray-600 mt-1"),a(b,"class","text-sm text-gray-500 mt-1"),a(w,"class","text-lg font-semibold text-blue-600"),a(I,"class","text-sm text-gray-500"),a(M,"class","text-sm text-green-600 mt-1"),a(h,"class","text-right"),a(l,"class","flex justify-between items-start"),a(e,"class","p-4 bg-blue-50 rounded-lg border border-blue-200")},m(V,q){x(V,e,q),i(e,l),i(l,n),i(n,r),i(r,s),i(n,c),i(n,u),i(u,m),i(n,g),i(n,b),i(b,y),i(l,S),i(l,h),i(h,w),i(w,R),i(h,z),i(h,I),i(I,P),i(I,C),i(I,O),i(h,D),i(h,M),i(M,H),i(M,j),i(e,$)},p(V,q){q[0]&64&&o!==(o=V[31].MainBenefit+"")&&T(s,o),q[0]&64&&d!==(d=V[31].MainBenefitEN+"")&&T(m,d),q[0]&64&&_!==(_=V[31].MainPlanLimitDesc+"")&&T(y,_),q[0]&64&&A!==(A=nt(parseInt(V[31].MainPlanAmount)||0)+"")&&T(R,A),q[0]&64&&E!==(E=V[31].MainPlanUnit1+"")&&T(P,E),q[0]&64&&k!==(k=V[31].MainPlanUnit2+"")&&T(O,k),q[0]&64&&L!==(L=nt(parseInt(V[31].MainPlanBalance)||0)+"")&&T(j,L)},d(V){V&&N(e)}}}function Ar(t){let e,l,n,r,o=X(t[5]),s=[];for(let c=0;c<o.length;c+=1)s[c]=Rr(Ir(t,o,c));return{c(){e=f("section"),l=f("h2"),l.textContent="รายละเอียดความคุ้มครอง",n=p(),r=f("div");for(let c=0;c<s.length;c+=1)s[c].c();a(l,"class","text-xl font-semibold text-gray-900 mb-4"),a(r,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6")},m(c,u){x(c,e,u),i(e,l),i(e,n),i(e,r);for(let d=0;d<s.length;d+=1)s[d]&&s[d].m(r,null)},p(c,u){if(u[0]&32){o=X(c[5]);let d;for(d=0;d<o.length;d+=1){const m=Ir(c,o,d);s[d]?s[d].p(m,u):(s[d]=Rr(m),s[d].c(),s[d].m(r,null))}for(;d<s.length;d+=1)s[d].d(1);s.length=o.length}},d(c){c&&N(e),Ie(s,c)}}}function Rr(t){let e,l,n,r,o=t[31].BenefitTH+"",s,c,u,d=t[31].BenefitEN+"",m,g,b,_=t[31].SubBenefitTH+"",y,S,h=t[31].SubBenefitEN+"",w,A,R,z,I,E,P,C,k=t[31].LimitAmt+"",O,D,M=t[31].LimitUnit+"",H,L,j,$,V,q,J=t[31].ComLimitAmt+"",W,K,re=t[31].ComLimitUnit+"",se,Q,G,B,Y,ae,de=t[31].BalComLimitAmt+"",Pe,ke,pe=t[31].BalComLimitUnit+"",Ne,De;return{c(){e=f("div"),l=f("div"),n=f("div"),r=f("h3"),s=v(o),c=p(),u=f("p"),m=v(d),g=p(),b=f("p"),y=v(_),S=v(" ("),w=v(h),A=v(")"),R=p(),z=f("div"),I=f("div"),E=f("span"),E.textContent="วงเงินต่อครั้ง:",P=p(),C=f("span"),O=v(k),D=p(),H=v(M),L=p(),j=f("div"),$=f("span"),$.textContent="วงเงินต่อปี:",V=p(),q=f("span"),W=v(J),K=p(),se=v(re),Q=p(),G=f("div"),B=f("span"),B.textContent="คงเหลือต่อปี:",Y=p(),ae=f("span"),Pe=v(de),ke=p(),Ne=v(pe),De=p(),a(r,"class","font-medium text-gray-900"),a(u,"class","text-sm text-gray-600"),a(b,"class","text-sm text-gray-500 mt-1"),a(E,"class","text-sm text-gray-500"),a(C,"class","text-sm font-medium"),a(I,"class","flex justify-between"),a($,"class","text-sm text-gray-500"),a(q,"class","text-sm font-medium"),a(j,"class","flex justify-between"),a(B,"class","text-sm text-gray-500"),a(ae,"class","text-sm font-medium text-green-600"),a(G,"class","flex justify-between"),a(z,"class","space-y-2"),a(l,"class","grid gap-4 md:grid-cols-2"),a(e,"class","p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors")},m(ee,le){x(ee,e,le),i(e,l),i(l,n),i(n,r),i(r,s),i(n,c),i(n,u),i(u,m),i(n,g),i(n,b),i(b,y),i(b,S),i(b,w),i(b,A),i(l,R),i(l,z),i(z,I),i(I,E),i(I,P),i(I,C),i(C,O),i(C,D),i(C,H),i(z,L),i(z,j),i(j,$),i(j,V),i(j,q),i(q,W),i(q,K),i(q,se),i(z,Q),i(z,G),i(G,B),i(G,Y),i(G,ae),i(ae,Pe),i(ae,ke),i(ae,Ne),i(e,De)},p(ee,le){le[0]&32&&o!==(o=ee[31].BenefitTH+"")&&T(s,o),le[0]&32&&d!==(d=ee[31].BenefitEN+"")&&T(m,d),le[0]&32&&_!==(_=ee[31].SubBenefitTH+"")&&T(y,_),le[0]&32&&h!==(h=ee[31].SubBenefitEN+"")&&T(w,h),le[0]&32&&k!==(k=ee[31].LimitAmt+"")&&T(O,k),le[0]&32&&M!==(M=ee[31].LimitUnit+"")&&T(H,M),le[0]&32&&J!==(J=ee[31].ComLimitAmt+"")&&T(W,J),le[0]&32&&re!==(re=ee[31].ComLimitUnit+"")&&T(se,re),le[0]&32&&de!==(de=ee[31].BalComLimitAmt+"")&&T(Pe,de),le[0]&32&&pe!==(pe=ee[31].BalComLimitUnit+"")&&T(Ne,pe)},d(ee){ee&&N(e)}}}function co(t){let e;return{c(){e=f("div"),e.innerHTML='<div class="text-4xl mb-2" aria-hidden="true">📋</div> <p class="text-gray-500">ไม่พบประวัติการเคลมสำหรับสมาชิกนี้</p>',a(e,"class","text-center py-8")},m(l,n){x(l,e,n)},p:F,d(l){l&&N(e)}}}function uo(t){let e,l=X(t[2]),n=[];for(let r=0;r<l.length;r+=1)n[r]=Or(Tr(t,l,r));return{c(){e=f("div");for(let r=0;r<n.length;r+=1)n[r].c();a(e,"class","space-y-3")},m(r,o){x(r,e,o);for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(e,null)},p(r,o){if(o[0]&4){l=X(r[2]);let s;for(s=0;s<l.length;s+=1){const c=Tr(r,l,s);n[s]?n[s].p(c,o):(n[s]=Or(c),n[s].c(),n[s].m(e,null))}for(;s<n.length;s+=1)n[s].d(1);n.length=l.length}},d(r){r&&N(e),Ie(n,r)}}}function Or(t){let e,l,n,r,o=t[28].ClaimNo+"",s,c,u,d=(t[28].DiagTH||t[28].DiagEN||"ไม่ระบุ")+"",m,g,b,_,y=Ur(t[28].VisitDate)+"",S,h,w,A,R=(t[28].ProviderTH||t[28].ProviderEN||"ไม่ระบุ")+"",z,I,E,P,C=nt(parseInt(t[28].PayableAmt)||0)+"",k,O,D,M,H=nt(parseInt(t[28].IncurredAmt)||0)+"",L,j,$,V=(t[28].ClaimStatus==="Paid"?"จ่ายแล้ว":t[28].ClaimStatus==="Pending"?"รอดำเนินการ":t[28].ClaimStatus==="Approved"?"อนุมัติแล้ว":t[28].ClaimStatus)+"",q,J,W,K,re,se,Q;function G(){return t[18](t[28])}return{c(){e=f("div"),l=f("div"),n=f("p"),r=v("เคลมเลขที่ "),s=v(o),c=p(),u=f("p"),m=v(d),g=p(),b=f("p"),_=v("วันที่เข้ารับการรักษา: "),S=v(y),h=p(),w=f("p"),A=v("โรงพยาบาล: "),z=v(R),I=p(),E=f("div"),P=f("p"),k=v(C),O=p(),D=f("p"),M=v("จากจำนวน "),L=v(H),j=p(),$=f("span"),q=v(V),W=p(),K=f("button"),K.textContent="ดูรายละเอียด",re=p(),a(n,"class","font-medium text-gray-900"),a(u,"class","text-sm text-gray-600"),a(b,"class","text-xs text-gray-500"),a(w,"class","text-xs text-gray-500"),a(P,"class","font-semibold text-gray-900"),a(D,"class","text-xs text-gray-500"),a($,"class",J="inline-block px-2 py-1 text-xs rounded-full mt-1 "+(t[28].ClaimStatus==="Paid"?"bg-green-100 text-green-800":t[28].ClaimStatus==="Pending"?"bg-yellow-100 text-yellow-800":t[28].ClaimStatus==="Approved"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800")),a(K,"class","block mt-1 text-xs text-blue-600 hover:text-blue-800"),a(E,"class","text-right"),a(e,"class","flex justify-between items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors")},m(B,Y){x(B,e,Y),i(e,l),i(l,n),i(n,r),i(n,s),i(l,c),i(l,u),i(u,m),i(l,g),i(l,b),i(b,_),i(b,S),i(l,h),i(l,w),i(w,A),i(w,z),i(e,I),i(e,E),i(E,P),i(P,k),i(E,O),i(E,D),i(D,M),i(D,L),i(E,j),i(E,$),i($,q),i(E,W),i(E,K),i(e,re),se||(Q=ie(K,"click",G),se=!0)},p(B,Y){t=B,Y[0]&4&&o!==(o=t[28].ClaimNo+"")&&T(s,o),Y[0]&4&&d!==(d=(t[28].DiagTH||t[28].DiagEN||"ไม่ระบุ")+"")&&T(m,d),Y[0]&4&&y!==(y=Ur(t[28].VisitDate)+"")&&T(S,y),Y[0]&4&&R!==(R=(t[28].ProviderTH||t[28].ProviderEN||"ไม่ระบุ")+"")&&T(z,R),Y[0]&4&&C!==(C=nt(parseInt(t[28].PayableAmt)||0)+"")&&T(k,C),Y[0]&4&&H!==(H=nt(parseInt(t[28].IncurredAmt)||0)+"")&&T(L,H),Y[0]&4&&V!==(V=(t[28].ClaimStatus==="Paid"?"จ่ายแล้ว":t[28].ClaimStatus==="Pending"?"รอดำเนินการ":t[28].ClaimStatus==="Approved"?"อนุมัติแล้ว":t[28].ClaimStatus)+"")&&T(q,V),Y[0]&4&&J!==(J="inline-block px-2 py-1 text-xs rounded-full mt-1 "+(t[28].ClaimStatus==="Paid"?"bg-green-100 text-green-800":t[28].ClaimStatus==="Pending"?"bg-yellow-100 text-yellow-800":t[28].ClaimStatus==="Approved"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"))&&a($,"class",J)},d(B){B&&N(e),se=!1,Q()}}}function Hr(t){let e,l,n,r,o=X(t[4]),s=[];for(let c=0;c<o.length;c+=1)s[c]=zr(Cr(t,o,c));return{c(){e=f("section"),l=f("h2"),l.textContent="เงื่อนไขสัญญา",n=p(),r=f("div");for(let c=0;c<s.length;c+=1)s[c].c();a(l,"class","text-xl font-semibold text-gray-900 mb-4"),a(r,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6")},m(c,u){x(c,e,u),i(e,l),i(e,n),i(e,r);for(let d=0;d<s.length;d+=1)s[d]&&s[d].m(r,null)},p(c,u){if(u[0]&16){o=X(c[4]);let d;for(d=0;d<o.length;d+=1){const m=Cr(c,o,d);s[d]?s[d].p(m,u):(s[d]=zr(m),s[d].c(),s[d].m(r,null))}for(;d<s.length;d+=1)s[d].d(1);s.length=o.length}},d(c){c&&N(e),Ie(s,c)}}}function Lr(t){let e,l,n=t[23].Remarks+"",r;return{c(){e=f("p"),l=v("หมายเหตุ: "),r=v(n),a(e,"class","text-xs text-gray-500 mt-2 italic")},m(o,s){x(o,e,s),i(e,l),i(e,r)},p(o,s){s[0]&16&&n!==(n=o[23].Remarks+"")&&T(r,n)},d(o){o&&N(e)}}}function zr(t){let e,l,n,r=(t[23].ConditionType==="Exclusion"?"ข้อยกเว้น":t[23].ConditionType==="Waiting Period"?"ระยะเวลารอคุ้มครอง":t[23].ConditionType)+"",o,s,c,u=t[23].ConditionApply==="Y"?"มีผล":"ไม่มีผล",d,m,g,b,_=t[23].ConditionDetail+"",y,S,h,w,A,R=Ce(t[23].EffFromDate)+"",z,I,E,P,C=Ce(t[23].EffToDate)+"",k,O,D,M,H=t[23].Remarks&&Lr(t);return{c(){e=f("div"),l=f("div"),n=f("h3"),o=v(r),s=p(),c=f("span"),d=v(u),g=p(),b=f("p"),y=v(_),S=p(),h=f("div"),w=f("div"),A=v("วันที่เริ่มต้น: "),z=v(R),I=p(),E=f("div"),P=v("วันที่สิ้นสุด: "),k=v(C),O=p(),H&&H.c(),D=p(),a(n,"class","font-medium text-gray-900"),a(c,"class",m="px-2 py-1 text-xs rounded-full "+(t[23].ConditionApply==="Y"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800")),a(l,"class","flex justify-between items-start mb-2"),a(b,"class","text-sm text-gray-700 mb-2"),a(h,"class","grid gap-2 sm:grid-cols-2 text-xs text-gray-500"),a(e,"class",M="p-4 border border-gray-200 rounded-lg "+(t[23].ConditionType==="Exclusion"?"bg-red-50 border-red-200":t[23].ConditionType==="Waiting Period"?"bg-yellow-50 border-yellow-200":"bg-gray-50"))},m(L,j){x(L,e,j),i(e,l),i(l,n),i(n,o),i(l,s),i(l,c),i(c,d),i(e,g),i(e,b),i(b,y),i(e,S),i(e,h),i(h,w),i(w,A),i(w,z),i(h,I),i(h,E),i(E,P),i(E,k),i(e,O),H&&H.m(e,null),i(e,D)},p(L,j){j[0]&16&&r!==(r=(L[23].ConditionType==="Exclusion"?"ข้อยกเว้น":L[23].ConditionType==="Waiting Period"?"ระยะเวลารอคุ้มครอง":L[23].ConditionType)+"")&&T(o,r),j[0]&16&&u!==(u=L[23].ConditionApply==="Y"?"มีผล":"ไม่มีผล")&&T(d,u),j[0]&16&&m!==(m="px-2 py-1 text-xs rounded-full "+(L[23].ConditionApply==="Y"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"))&&a(c,"class",m),j[0]&16&&_!==(_=L[23].ConditionDetail+"")&&T(y,_),j[0]&16&&R!==(R=Ce(L[23].EffFromDate)+"")&&T(z,R),j[0]&16&&C!==(C=Ce(L[23].EffToDate)+"")&&T(k,C),L[23].Remarks?H?H.p(L,j):(H=Lr(L),H.c(),H.m(e,D)):H&&(H.d(1),H=null),j[0]&16&&M!==(M="p-4 border border-gray-200 rounded-lg "+(L[23].ConditionType==="Exclusion"?"bg-red-50 border-red-200":L[23].ConditionType==="Waiting Period"?"bg-yellow-50 border-yellow-200":"bg-gray-50"))&&a(e,"class",M)},d(L){L&&N(e),H&&H.d()}}}function Br(t){let e,l,n,r,o=X(t[3]),s=[];for(let c=0;c<o.length;c+=1)s[c]=jr(Er(t,o,c));return{c(){e=f("section"),l=f("h2"),l.textContent="เงื่อนไขเฉพาะสมาชิก",n=p(),r=f("div");for(let c=0;c<s.length;c+=1)s[c].c();a(l,"class","text-xl font-semibold text-gray-900 mb-4"),a(r,"class","space-y-4"),a(e,"class","bg-white rounded-lg shadow-sm border border-gray-200 p-6")},m(c,u){x(c,e,u),i(e,l),i(e,n),i(e,r);for(let d=0;d<s.length;d+=1)s[d]&&s[d].m(r,null)},p(c,u){if(u[0]&8){o=X(c[3]);let d;for(d=0;d<o.length;d+=1){const m=Er(c,o,d);s[d]?s[d].p(m,u):(s[d]=jr(m),s[d].c(),s[d].m(r,null))}for(;d<s.length;d+=1)s[d].d(1);s.length=o.length}},d(c){c&&N(e),Ie(s,c)}}}function Vr(t){let e,l,n=t[23].Remarks+"",r;return{c(){e=f("p"),l=v("หมายเหตุ: "),r=v(n),a(e,"class","text-xs text-gray-500 mt-2 italic")},m(o,s){x(o,e,s),i(e,l),i(e,r)},p(o,s){s[0]&8&&n!==(n=o[23].Remarks+"")&&T(r,n)},d(o){o&&N(e)}}}function jr(t){let e,l,n,r=(t[23].ConditionType==="Medical History"?"ประวัติการรักษา":t[23].ConditionType==="Medication"?"การใช้ยา":t[23].ConditionType)+"",o,s,c,u=(t[23].Action==="Monitor"?"ต้องติดตาม":t[23].Action)+"",d,m,g,b,_=t[23].ConditionDetail+"",y,S,h,w,A,R=Ce(t[23].EffFromDate)+"",z,I,E,P,C=Ce(t[23].EffToDate)+"",k,O,D,M,H=t[23].Remarks&&Vr(t);return{c(){e=f("div"),l=f("div"),n=f("h3"),o=v(r),s=p(),c=f("span"),d=v(u),g=p(),b=f("p"),y=v(_),S=p(),h=f("div"),w=f("div"),A=v("วันที่เริ่มต้น: "),z=v(R),I=p(),E=f("div"),P=v("วันที่สิ้นสุด: "),k=v(C),O=p(),H&&H.c(),D=p(),a(n,"class","font-medium text-gray-900"),a(c,"class",m="px-2 py-1 text-xs rounded-full "+(t[23].Action==="Monitor"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800")),a(l,"class","flex justify-between items-start mb-2"),a(b,"class","text-sm text-gray-700 mb-2"),a(h,"class","grid gap-2 sm:grid-cols-2 text-xs text-gray-500"),a(e,"class",M="p-4 border border-gray-200 rounded-lg "+(t[23].ConditionType==="Medical History"?"bg-blue-50 border-blue-200":t[23].ConditionType==="Medication"?"bg-purple-50 border-purple-200":"bg-gray-50"))},m(L,j){x(L,e,j),i(e,l),i(l,n),i(n,o),i(l,s),i(l,c),i(c,d),i(e,g),i(e,b),i(b,y),i(e,S),i(e,h),i(h,w),i(w,A),i(w,z),i(h,I),i(h,E),i(E,P),i(E,k),i(e,O),H&&H.m(e,null),i(e,D)},p(L,j){j[0]&8&&r!==(r=(L[23].ConditionType==="Medical History"?"ประวัติการรักษา":L[23].ConditionType==="Medication"?"การใช้ยา":L[23].ConditionType)+"")&&T(o,r),j[0]&8&&u!==(u=(L[23].Action==="Monitor"?"ต้องติดตาม":L[23].Action)+"")&&T(d,u),j[0]&8&&m!==(m="px-2 py-1 text-xs rounded-full "+(L[23].Action==="Monitor"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"))&&a(c,"class",m),j[0]&8&&_!==(_=L[23].ConditionDetail+"")&&T(y,_),j[0]&8&&R!==(R=Ce(L[23].EffFromDate)+"")&&T(z,R),j[0]&8&&C!==(C=Ce(L[23].EffToDate)+"")&&T(k,C),L[23].Remarks?H?H.p(L,j):(H=Vr(L),H.c(),H.m(e,D)):H&&(H.d(1),H=null),j[0]&8&&M!==(M="p-4 border border-gray-200 rounded-lg "+(L[23].ConditionType==="Medical History"?"bg-blue-50 border-blue-200":L[23].ConditionType==="Medication"?"bg-purple-50 border-purple-200":"bg-gray-50"))&&a(e,"class",M)},d(L){L&&N(e),H&&H.d()}}}function fo(t){let e,l,n,r;const o=[oo,ao,so,ro,io],s=[];function c(u,d){return u[0]?u[10]?1:u[9]?2:u[7]?4:3:0}return e=c(t),l=s[e]=o[e](t),{c(){l.c(),n=kl()},m(u,d){s[e].m(u,d),x(u,n,d),r=!0},p(u,d){let m=e;e=c(u),e===m?s[e].p(u,d):(rn(),be(s[m],1,1,()=>{s[m]=null}),sn(),l=s[e],l?l.p(u,d):(l=s[e]=o[e](u),l.c()),ue(l,1),l.m(n.parentNode,n))},i(u){r||(ue(l),r=!0)},o(u){be(l),r=!1},d(u){u&&N(n),s[e].d(u)}}}function nt(t){return t?new Intl.NumberFormat("th-TH",{style:"currency",currency:"THB",minimumFractionDigits:0,maximumFractionDigits:0}).format(t):"ไม่ระบุ"}function Ce(t){return t?new Date(t).toLocaleDateString("th-TH",{day:"2-digit",month:"2-digit",year:"numeric"}):"ไม่ระบุ"}function Ur(t){return t?new Date(t).toLocaleDateString("th-TH",{day:"2-digit",month:"2-digit",year:"numeric"}):"ไม่ระบุ"}function mo(t){alert(`ฟังก์ชันดูรายละเอียดเคลม ${t} จะถูกพัฒนาในอนาคต`)}function po(t,e,l){let n,r,o,s,c,u,d,m,g,b,_,y,S,h;Te(t,dn,k=>l(15,y=k)),Te(t,me,k=>l(16,S=k)),Te(t,kt,k=>l(17,h=k));const w=nn();let A=null;function R(){w("navigate",{page:"policy-list"})}const z={Active:"bg-green-100 text-green-800 border-green-200",Inactive:"bg-gray-100 text-gray-800 border-gray-200",Expired:"bg-red-100 text-red-800 border-red-200",Pending:"bg-yellow-100 text-yellow-800 border-yellow-200",Cancelled:"bg-gray-100 text-gray-800 border-gray-200"};async function I(){if(s!=null&&s.memberCode)try{await va(s.memberCode),console.log("Policy detail loaded successfully for member:",s.memberCode)}catch(k){console.error("Failed to load policy detail:",k)}}async function E(){if(!(!(s!=null&&s.insurerCode)||!(s!=null&&s.citizenID)))try{const k=await Ye.policies.searchByCitizenId(s.citizenID,s.insurerCode);if(k.success&&k.data&&k.data.length>0){const O=k.data[0];l(1,A=O),console.log("Policy list data loaded successfully for member:",s.memberCode,O)}else console.warn("No policy data found for member:",s.memberCode),l(1,A=null)}catch(k){console.error("Failed to load policy list data:",k),l(1,A=null)}}async function P(){await I(),await E()}const C=k=>mo(k.ClaimNo);return t.$$.update=()=>{t.$$.dirty[0]&131072&&l(14,n=h.data),t.$$.dirty[0]&131072&&l(10,r=h.loading),t.$$.dirty[0]&131072&&l(9,o=h.error),t.$$.dirty[0]&65536&&l(0,s=S),t.$$.dirty[0]&32768&&l(8,c=y),t.$$.dirty[0]&1&&s!=null&&s.memberCode&&I(),t.$$.dirty[0]&1&&s!=null&&s.insurerCode&&s!=null&&s.citizenID&&E(),t.$$.dirty[0]&16384&&l(7,u=n),t.$$.dirty[0]&16384&&l(6,d=(n==null?void 0:n.ListPolicyDetail)||[]),t.$$.dirty[0]&16384&&l(5,m=(n==null?void 0:n.BenefitList)||[]),t.$$.dirty[0]&16384&&l(4,g=(n==null?void 0:n.ListContractCondition)||[]),t.$$.dirty[0]&16384&&l(3,b=(n==null?void 0:n.ListMemberCondition)||[]),t.$$.dirty[0]&16384&&l(2,_=(n==null?void 0:n.ListClaimHistory)||[])},[s,A,_,b,g,m,d,u,c,o,r,R,z,P,n,y,S,h,C]}class go extends Mt{constructor(e){super(),xt(this,e,po,fo,pt,{},null,[-1,-1])}}function Fr(t){let e,l,n,r=t[0].replace("-"," ")+"",o;return{c(){e=f("span"),e.textContent="/",l=p(),n=f("span"),o=v(r),a(e,"class","text-gray-300 flex-shrink-0"),a(e,"aria-hidden","true"),a(n,"class","text-sm text-gray-600 capitalize flex-shrink-0")},m(s,c){x(s,e,c),x(s,l,c),x(s,n,c),i(n,o)},p(s,c){c&1&&r!==(r=s[0].replace("-"," ")+"")&&T(o,r)},d(s){s&&(N(e),N(l),N(n))}}}function bo(t){let e,l;return e=new ds({}),e.$on("navigate",t[6]),{c(){Ke(e.$$.fragment)},m(n,r){Le(e,n,r),l=!0},p:F,i(n){l||(ue(e.$$.fragment,n),l=!0)},o(n){be(e.$$.fragment,n),l=!1},d(n){ze(e,n)}}}function ho(t){let e,l;return e=new go({props:{selectedPolicyId:t[1],selectedMemberCode:t[2]}}),e.$on("navigate",t[6]),{c(){Ke(e.$$.fragment)},m(n,r){Le(e,n,r),l=!0},p(n,r){const o={};r&2&&(o.selectedPolicyId=n[1]),r&4&&(o.selectedMemberCode=n[2]),e.$set(o)},i(n){l||(ue(e.$$.fragment,n),l=!0)},o(n){be(e.$$.fragment,n),l=!1},d(n){ze(e,n)}}}function _o(t){let e,l;return e=new ds({}),e.$on("navigate",t[6]),{c(){Ke(e.$$.fragment)},m(n,r){Le(e,n,r),l=!0},p:F,i(n){l||(ue(e.$$.fragment,n),l=!0)},o(n){be(e.$$.fragment,n),l=!1},d(n){ze(e,n)}}}function yo(t){let e,l,n,r,o,s,c,u,d,m,g,b,_,y=(t[3]||"Guest")+"",S,h,w,A,R=(t[5]?t[5].nameEN?t[5].nameEN.charAt(0):t[5].nameTH?t[5].nameTH.charAt(0):t[5].memberCode.charAt(3):"G")+"",z,I,E,P,C,k,O,D,M,H=t[0]!=="policy-list"&&Fr(t);const L=[_o,ho,bo],j=[];function $(V,q){return V[0]==="policy-list"?0:V[0]==="policy-detail"?1:2}return C=$(t),k=j[C]=L[C](t),{c(){e=f("div"),l=f("nav"),n=f("div"),r=f("div"),o=f("div"),s=f("button"),s.textContent="🛡️ Insurance Portal",c=p(),H&&H.c(),u=p(),d=f("div"),m=f("div"),g=f("span"),g.textContent="Welcome back,",b=p(),_=f("span"),S=v(y),h=p(),w=f("div"),A=f("span"),z=v(R),E=p(),P=f("div"),k.c(),a(s,"class","text-xl font-semibold text-gray-900 hover:text-blue-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1 flex-shrink-0"),a(s,"aria-label","Go to home page"),a(o,"class","flex items-center space-x-4 lg:space-x-6 flex-1 min-w-0"),a(g,"class","text-sm text-gray-500"),a(_,"class","text-sm font-medium text-gray-700"),a(m,"class","hidden lg:flex items-center space-x-2"),a(A,"class","text-white text-sm font-medium"),a(w,"class","w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"),a(w,"title",I=t[4]||"No member selected"),a(d,"class","flex items-center space-x-4 flex-shrink-0"),a(r,"class","flex justify-between items-center h-16"),a(n,"class","max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"),a(l,"class","bg-white shadow-sm border-b border-gray-200"),a(P,"class","flex-1"),a(e,"class","min-h-screen bg-gray-50")},m(V,q){x(V,e,q),i(e,l),i(l,n),i(n,r),i(r,o),i(o,s),i(o,c),H&&H.m(o,null),i(r,u),i(r,d),i(d,m),i(m,g),i(m,b),i(m,_),i(_,S),i(d,h),i(d,w),i(w,A),i(A,z),i(e,E),i(e,P),j[C].m(P,null),O=!0,D||(M=ie(s,"click",t[7]),D=!0)},p(V,[q]){V[0]!=="policy-list"?H?H.p(V,q):(H=Fr(V),H.c(),H.m(o,null)):H&&(H.d(1),H=null),(!O||q&8)&&y!==(y=(V[3]||"Guest")+"")&&T(S,y),(!O||q&32)&&R!==(R=(V[5]?V[5].nameEN?V[5].nameEN.charAt(0):V[5].nameTH?V[5].nameTH.charAt(0):V[5].memberCode.charAt(3):"G")+"")&&T(z,R),(!O||q&16&&I!==(I=V[4]||"No member selected"))&&a(w,"title",I);let J=C;C=$(V),C===J?j[C].p(V,q):(rn(),be(j[J],1,1,()=>{j[J]=null}),sn(),k=j[C],k?k.p(V,q):(k=j[C]=L[C](V),k.c()),ue(k,1),k.m(P,null))},i(V){O||(ue(k),O=!0)},o(V){be(k),O=!1},d(V){V&&N(e),H&&H.d(),j[C].d(),D=!1,M()}}}function vo(t,e,l){let n,r,o;Te(t,da,_=>l(3,n=_)),Te(t,dn,_=>l(4,r=_)),Te(t,me,_=>l(5,o=_));const s={name:"Insurance Portal",version:"1.0.0",description:"Comprehensive insurance management platform built with Svelte and Tailwind CSS"};let c="policy-list",u=null,d=null;function m(_){l(0,c=_.detail.page),l(1,u=_.detail.policyId||null),l(2,d=_.detail.memberCode||null),console.log("Navigating to:",c,u?`with policy ID: ${u}`:"",d?`with member code: ${d}`:"")}const g={"policy-list":"Policy List - Insurance Portal","policy-detail":"Policy Detail - Insurance Portal"};Cl(async()=>{try{await ba(),console.log("Member store initialized successfully")}catch(_){console.error("Failed to initialize member store:",_)}}),console.log("App initialized:",s);const b=()=>m({detail:{page:"policy-list"}});return t.$$.update=()=>{t.$$.dirty&1&&typeof document<"u"&&(document.title=g[c]||"Insurance Portal")},[c,u,d,n,r,o,m,b]}class wo extends Mt{constructor(e){super(),xt(this,e,vo,yo,pt,{})}}new wo({target:document.getElementById("app")});
